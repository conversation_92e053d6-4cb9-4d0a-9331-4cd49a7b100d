{"code":"ER_ACCESS_DENIED_ERROR","errno":1045,"level":"error","message":"Database connection failed: Access denied for user 'root'@'localhost' (using password: NO)","service":"sms-backend","sqlMessage":"Access denied for user 'root'@'localhost' (using password: NO)","sqlState":"28000","stack":"Error: Access denied for user 'root'@'localhost' (using password: NO)\n    at Packet.asError (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\packets\\packet.js:740:17)\n    at ClientHandshake.execute (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\commands\\command.js:29:26)\n    at PoolConnection.handlePacket (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\base\\connection.js:475:34)\n    at PacketParser.onPacket (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\base\\connection.js:93:12)\n    at PacketParser.executeStart (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\packet_parser.js:75:16)\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\base\\connection.js:100:25)\n    at Socket.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)\n    at Readable.push (node:internal/streams/readable:392:5)","timestamp":"2025-07-22 14:21:53"}
{"code":"ER_ACCESS_DENIED_ERROR","errno":1045,"level":"error","message":"Database connection failed: Access denied for user 'root'@'localhost' (using password: NO)","service":"sms-backend","sqlMessage":"Access denied for user 'root'@'localhost' (using password: NO)","sqlState":"28000","stack":"Error: Access denied for user 'root'@'localhost' (using password: NO)\n    at Packet.asError (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\packets\\packet.js:740:17)\n    at ClientHandshake.execute (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\commands\\command.js:29:26)\n    at PoolConnection.handlePacket (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\base\\connection.js:475:34)\n    at PacketParser.onPacket (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\base\\connection.js:93:12)\n    at PacketParser.executeStart (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\packet_parser.js:75:16)\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\base\\connection.js:100:25)\n    at Socket.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)\n    at Readable.push (node:internal/streams/readable:392:5)","timestamp":"2025-07-22 14:22:35"}
{"code":"ER_ACCESS_DENIED_ERROR","errno":1045,"level":"error","message":"Database connection failed: Access denied for user 'root'@'localhost' (using password: NO)","service":"sms-backend","sqlMessage":"Access denied for user 'root'@'localhost' (using password: NO)","sqlState":"28000","stack":"Error: Access denied for user 'root'@'localhost' (using password: NO)\n    at Packet.asError (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\packets\\packet.js:740:17)\n    at ClientHandshake.execute (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\commands\\command.js:29:26)\n    at PoolConnection.handlePacket (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\base\\connection.js:475:34)\n    at PacketParser.onPacket (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\base\\connection.js:93:12)\n    at PacketParser.executeStart (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\packet_parser.js:75:16)\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\base\\connection.js:100:25)\n    at Socket.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)\n    at Readable.push (node:internal/streams/readable:392:5)","timestamp":"2025-07-23 14:26:46"}
{"code":"ER_BAD_DB_ERROR","errno":1049,"level":"error","message":"Database connection failed: Unknown database 'school_management_system'","service":"sms-backend","sqlMessage":"Unknown database 'school_management_system'","sqlState":"42000","stack":"Error: Unknown database 'school_management_system'\n    at Packet.asError (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\packets\\packet.js:740:17)\n    at ClientHandshake.execute (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\commands\\command.js:29:26)\n    at PoolConnection.handlePacket (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\base\\connection.js:475:34)\n    at PacketParser.onPacket (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\base\\connection.js:93:12)\n    at PacketParser.executeStart (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\packet_parser.js:75:16)\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\base\\connection.js:100:25)\n    at Socket.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)\n    at Readable.push (node:internal/streams/readable:392:5)","timestamp":"2025-07-23 14:28:31"}
{"code":"ER_BAD_DB_ERROR","errno":1049,"level":"error","message":"Database connection failed: Unknown database 'school_management_system'","service":"sms-backend","sqlMessage":"Unknown database 'school_management_system'","sqlState":"42000","stack":"Error: Unknown database 'school_management_system'\n    at Packet.asError (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\packets\\packet.js:740:17)\n    at ClientHandshake.execute (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\commands\\command.js:29:26)\n    at PoolConnection.handlePacket (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\base\\connection.js:475:34)\n    at PacketParser.onPacket (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\base\\connection.js:93:12)\n    at PacketParser.executeStart (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\packet_parser.js:75:16)\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\base\\connection.js:100:25)\n    at Socket.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)\n    at Readable.push (node:internal/streams/readable:392:5)","timestamp":"2025-07-23 14:28:35"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-23 14:30:53"}
{"level":"info","message":"Database connected successfully","service":"sms-backend","timestamp":"2025-07-23 14:30:53"}
{"level":"info","message":"Server running on port 5000 in development mode","service":"sms-backend","timestamp":"2025-07-23 14:30:53"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-23 14:36:13"}
{"level":"info","message":"Database connected successfully","service":"sms-backend","timestamp":"2025-07-23 14:36:13"}
{"level":"info","message":"Server running on port 5000 in development mode","service":"sms-backend","timestamp":"2025-07-23 14:36:13"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-23 14:36:47"}
{"level":"info","message":"Database connected successfully","service":"sms-backend","timestamp":"2025-07-23 14:36:47"}
{"level":"info","message":"Server running on port 5000 in development mode","service":"sms-backend","timestamp":"2025-07-23 14:36:47"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-23 14:37:32"}
{"level":"info","message":"Database connected successfully","service":"sms-backend","timestamp":"2025-07-23 14:37:32"}
{"level":"info","message":"Server running on port 5000 in development mode","service":"sms-backend","timestamp":"2025-07-23 14:37:32"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-23 14:38:12"}
{"level":"info","message":"Database connected successfully","service":"sms-backend","timestamp":"2025-07-23 14:38:12"}
{"level":"info","message":"Server running on port 5000 in development mode","service":"sms-backend","timestamp":"2025-07-23 14:38:12"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-23 14:38:52"}
{"level":"info","message":"Database connected successfully","service":"sms-backend","timestamp":"2025-07-23 14:38:52"}
{"level":"info","message":"Server running on port 5000 in development mode","service":"sms-backend","timestamp":"2025-07-23 14:38:52"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-23 14:39:34"}
{"level":"info","message":"Database connected successfully","service":"sms-backend","timestamp":"2025-07-23 14:39:34"}
{"level":"info","message":"Server running on port 5000 in development mode","service":"sms-backend","timestamp":"2025-07-23 14:39:34"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-23 14:40:26"}
{"level":"info","message":"Database connected successfully","service":"sms-backend","timestamp":"2025-07-23 14:40:26"}
{"level":"info","message":"Server running on port 5000 in development mode","service":"sms-backend","timestamp":"2025-07-23 14:40:26"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-23 14:41:21"}
{"level":"info","message":"Database connected successfully","service":"sms-backend","timestamp":"2025-07-23 14:41:21"}
{"level":"info","message":"Server running on port 5000 in development mode","service":"sms-backend","timestamp":"2025-07-23 14:41:21"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-23 14:41:39"}
{"level":"info","message":"Database connected successfully","service":"sms-backend","timestamp":"2025-07-23 14:41:39"}
{"level":"info","message":"Server running on port 5000 in development mode","service":"sms-backend","timestamp":"2025-07-23 14:41:39"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-23 14:42:03"}
{"level":"info","message":"Database connected successfully","service":"sms-backend","timestamp":"2025-07-23 14:42:03"}
{"level":"info","message":"Server running on port 5000 in development mode","service":"sms-backend","timestamp":"2025-07-23 14:42:03"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-23 14:56:13"}
{"level":"info","message":"Database connected successfully","service":"sms-backend","timestamp":"2025-07-23 14:56:13"}
{"level":"info","message":"Server running on port 5000 in development mode","service":"sms-backend","timestamp":"2025-07-23 14:56:13"}
{"level":"info","message":"SIGINT received. Shutting down gracefully...","service":"sms-backend","timestamp":"2025-07-23 14:56:49"}
{"level":"info","message":"Starting database migration process...","service":"sms-backend","timestamp":"2025-07-23 14:57:01"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-23 14:57:01"}
{"level":"info","message":"Database connection established","service":"sms-backend","timestamp":"2025-07-23 14:57:01"}
{"level":"info","message":"Migrations table created or already exists","service":"sms-backend","timestamp":"2025-07-23 14:57:01"}
{"level":"info","message":"Found 7 pending migrations","service":"sms-backend","timestamp":"2025-07-23 14:57:01"}
{"level":"info","message":"Executing migration: 001_create_users_and_auth_tables.js","service":"sms-backend","timestamp":"2025-07-23 14:57:01"}
{"level":"info","message":"Creating users and authentication tables...","service":"sms-backend","timestamp":"2025-07-23 14:57:01"}
{"level":"info","message":"Users and authentication tables created successfully","service":"sms-backend","timestamp":"2025-07-23 14:57:02"}
{"level":"info","message":"Migration completed: 001_create_users_and_auth_tables.js","service":"sms-backend","timestamp":"2025-07-23 14:57:02"}
{"level":"info","message":"Executing migration: 002_create_academic_structure_tables.js","service":"sms-backend","timestamp":"2025-07-23 14:57:02"}
{"level":"info","message":"Creating academic structure tables...","service":"sms-backend","timestamp":"2025-07-23 14:57:02"}
{"level":"info","message":"Academic structure tables created successfully","service":"sms-backend","timestamp":"2025-07-23 14:57:02"}
{"level":"info","message":"Migration completed: 002_create_academic_structure_tables.js","service":"sms-backend","timestamp":"2025-07-23 14:57:02"}
{"level":"info","message":"Executing migration: 003_create_assessment_and_grading_tables.js","service":"sms-backend","timestamp":"2025-07-23 14:57:02"}
{"level":"info","message":"Creating assessment and grading tables...","service":"sms-backend","timestamp":"2025-07-23 14:57:02"}
{"error":"Expression of generated column 'percentage' contains a disallowed function.","level":"error","message":"Database query error:","params":[],"query":"\n    CREATE TABLE IF NOT EXISTS assessment_results (\n      id INT AUTO_INCREMENT PRIMARY KEY,\n      assessment_id INT NOT NULL,\n      student_id INT NOT NULL,\n      marks_obtained DECIMAL(5,2) NOT NULL,\n      percentage DECIMAL(5,2) GENERATED ALWAYS AS ((marks_obtained / (SELECT total_marks FROM assessments WHERE id = assessment_id)) * 100) STORED,\n      grade VARCHAR(5),\n      remarks TEXT,\n      is_absent BOOLEAN DEFAULT FALSE,\n      submission_date TIMESTAMP,\n      graded_by INT,\n      graded_at TIMESTAMP,\n      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,\n      \n      FOREIGN KEY (assessment_id) REFERENCES assessments(id) ON DELETE CASCADE,\n      FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE,\n      FOREIGN KEY (graded_by) REFERENCES users(id) ON DELETE SET NULL,\n      INDEX idx_assessment_id (assessment_id),\n      INDEX idx_student_id (student_id),\n      INDEX idx_marks_obtained (marks_obtained),\n      INDEX idx_percentage (percentage),\n      INDEX idx_grade (grade),\n      INDEX idx_is_absent (is_absent),\n      UNIQUE KEY unique_assessment_student (assessment_id, student_id)\n    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;\n  ","service":"sms-backend","timestamp":"2025-07-23 14:57:02"}
{"level":"info","message":"Starting database migration process...","service":"sms-backend","timestamp":"2025-07-23 15:03:46"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-23 15:03:46"}
{"level":"info","message":"Database connection established","service":"sms-backend","timestamp":"2025-07-23 15:03:46"}
{"level":"info","message":"Migrations table created or already exists","service":"sms-backend","timestamp":"2025-07-23 15:03:46"}
{"level":"info","message":"Found 5 pending migrations","service":"sms-backend","timestamp":"2025-07-23 15:03:46"}
{"level":"info","message":"Executing migration: 003_create_assessment_and_grading_tables.js","service":"sms-backend","timestamp":"2025-07-23 15:03:46"}
{"level":"info","message":"Creating assessment and grading tables...","service":"sms-backend","timestamp":"2025-07-23 15:03:46"}
{"level":"info","message":"Assessment and grading tables created successfully","service":"sms-backend","timestamp":"2025-07-23 15:03:46"}
{"level":"info","message":"Migration completed: 003_create_assessment_and_grading_tables.js","service":"sms-backend","timestamp":"2025-07-23 15:03:46"}
{"level":"info","message":"Executing migration: 004_create_attendance_and_timetable_tables.js","service":"sms-backend","timestamp":"2025-07-23 15:03:46"}
{"level":"info","message":"Creating attendance and timetable tables...","service":"sms-backend","timestamp":"2025-07-23 15:03:46"}
{"level":"info","message":"Attendance and timetable tables created successfully","service":"sms-backend","timestamp":"2025-07-23 15:03:47"}
{"level":"info","message":"Migration completed: 004_create_attendance_and_timetable_tables.js","service":"sms-backend","timestamp":"2025-07-23 15:03:47"}
{"level":"info","message":"Executing migration: 005_create_health_and_library_tables.js","service":"sms-backend","timestamp":"2025-07-23 15:03:47"}
{"level":"info","message":"Creating health records and library tables...","service":"sms-backend","timestamp":"2025-07-23 15:03:47"}
{"level":"info","message":"Health records and library tables created successfully","service":"sms-backend","timestamp":"2025-07-23 15:03:48"}
{"level":"info","message":"Migration completed: 005_create_health_and_library_tables.js","service":"sms-backend","timestamp":"2025-07-23 15:03:48"}
{"level":"info","message":"Starting database migration process...","service":"sms-backend","timestamp":"2025-07-23 15:15:06"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-23 15:15:06"}
{"level":"info","message":"Database connection established","service":"sms-backend","timestamp":"2025-07-23 15:15:06"}
{"level":"info","message":"Migrations table created or already exists","service":"sms-backend","timestamp":"2025-07-23 15:15:06"}
{"level":"info","message":"Found 2 pending migrations","service":"sms-backend","timestamp":"2025-07-23 15:15:06"}
{"level":"info","message":"Executing migration: 006_create_transportation_and_communication_tables.js","service":"sms-backend","timestamp":"2025-07-23 15:15:06"}
{"level":"info","message":"Creating transportation and communication tables...","service":"sms-backend","timestamp":"2025-07-23 15:15:06"}
{"level":"info","message":"Transportation and communication tables created successfully","service":"sms-backend","timestamp":"2025-07-23 15:15:06"}
{"level":"info","message":"Migration completed: 006_create_transportation_and_communication_tables.js","service":"sms-backend","timestamp":"2025-07-23 15:15:06"}
{"level":"info","message":"Executing migration: 007_create_fees_and_admin_tables.js","service":"sms-backend","timestamp":"2025-07-23 15:15:06"}
{"level":"info","message":"Creating fees and administrative tables...","service":"sms-backend","timestamp":"2025-07-23 15:15:06"}
{"level":"info","message":"Fees and administrative tables created successfully","service":"sms-backend","timestamp":"2025-07-23 15:15:07"}
{"level":"info","message":"Migration completed: 007_create_fees_and_admin_tables.js","service":"sms-backend","timestamp":"2025-07-23 15:15:07"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-23 15:15:39"}
{"level":"info","message":"Database connected successfully","service":"sms-backend","timestamp":"2025-07-23 15:15:39"}
{"level":"info","message":"Server running on port 5000 in development mode","service":"sms-backend","timestamp":"2025-07-23 15:15:39"}
{"level":"error","message":"Authentication error: Invalid token","service":"sms-backend","stack":"Error: Invalid token\n    at verifyAccessToken (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\utils\\jwt.js:63:13)\n    at authenticate (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\middleware\\auth.js:22:21)\n    at newFn (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\index.js:47:12)","timestamp":"2025-07-23 15:16:29"}
{"level":"info","message":"::1 - - [23/Jul/2025:14:16:29 +0000] \"GET /api/teachers?page=1&limit=10&search=&sort_by=first_name&sort_order=asc HTTP/1.1\" 401 66 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-23 15:16:29"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-23 15:28:25"}
{"level":"info","message":"Database connected successfully","service":"sms-backend","timestamp":"2025-07-23 15:28:25"}
{"level":"info","message":"Server running on port 5000 in development mode","service":"sms-backend","timestamp":"2025-07-23 15:28:25"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error: Expected ',' or '}' after property value in JSON at position 130 (line 7 column 3)","method":"POST","service":"sms-backend","stack":"SyntaxError: Expected ',' or '}' after property value in JSON at position 130 (line 7 column 3)\n    at JSON.parse (<anonymous>)\n    at parse (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\body-parser\\lib\\types\\json.js:92:19)\n    at C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\body-parser\\lib\\read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:211:14)\n    at invokeCallback (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\raw-body\\index.js:238:16)\n    at done (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)","timestamp":"2025-07-23 15:29:12","url":"/api/auth","userAgent":"Thunder Client (https://www.thunderclient.com)"}
{"level":"info","message":"::ffff:127.0.0.1 - - [23/Jul/2025:14:29:12 +0000] \"POST /api/auth HTTP/1.1\" 400 - \"-\" \"Thunder Client (https://www.thunderclient.com)\"","service":"sms-backend","timestamp":"2025-07-23 15:29:12"}
{"level":"info","message":"::ffff:127.0.0.1 - - [23/Jul/2025:14:29:26 +0000] \"POST /api/auth HTTP/1.1\" 404 45 \"-\" \"Thunder Client (https://www.thunderclient.com)\"","service":"sms-backend","timestamp":"2025-07-23 15:29:26"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-23 15:29:46"}
{"level":"info","message":"Database connected successfully","service":"sms-backend","timestamp":"2025-07-23 15:29:46"}
{"level":"info","message":"Server running on port 5000 in development mode","service":"sms-backend","timestamp":"2025-07-23 15:29:46"}
{"level":"info","message":"::ffff:127.0.0.1 - - [23/Jul/2025:14:29:49 +0000] \"POST /api/auth HTTP/1.1\" 404 45 \"-\" \"Thunder Client (https://www.thunderclient.com)\"","service":"sms-backend","timestamp":"2025-07-23 15:29:49"}
{"level":"error","message":"Authentication error: Invalid token","service":"sms-backend","stack":"Error: Invalid token\n    at verifyAccessToken (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\utils\\jwt.js:63:13)\n    at authenticate (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\middleware\\auth.js:22:21)\n    at newFn (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\index.js:47:12)","timestamp":"2025-07-23 15:30:37"}
{"level":"info","message":"::ffff:127.0.0.1 - - [23/Jul/2025:14:30:37 +0000] \"POST /api/admin HTTP/1.1\" 401 66 \"-\" \"Thunder Client (https://www.thunderclient.com)\"","service":"sms-backend","timestamp":"2025-07-23 15:30:37"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-23 15:31:02"}
{"level":"info","message":"Database connected successfully","service":"sms-backend","timestamp":"2025-07-23 15:31:02"}
{"level":"info","message":"Server running on port 5000 in development mode","service":"sms-backend","timestamp":"2025-07-23 15:31:02"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-23 15:31:05"}
{"level":"info","message":"Database connected successfully","service":"sms-backend","timestamp":"2025-07-23 15:31:05"}
{"level":"info","message":"Server running on port 5000 in development mode","service":"sms-backend","timestamp":"2025-07-23 15:31:05"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-23 15:31:20"}
{"level":"info","message":"Database connected successfully","service":"sms-backend","timestamp":"2025-07-23 15:31:20"}
{"level":"info","message":"Server running on port 5000 in development mode","service":"sms-backend","timestamp":"2025-07-23 15:31:20"}
{"level":"info","message":"::ffff:127.0.0.1 - - [23/Jul/2025:14:31:24 +0000] \"POST /api/admin HTTP/1.1\" 400 494 \"-\" \"Thunder Client (https://www.thunderclient.com)\"","service":"sms-backend","timestamp":"2025-07-23 15:31:24"}
{"code":"ER_NO_DEFAULT_FOR_FIELD","errno":1364,"level":"error","message":"Transaction error: Field 'uuid' doesn't have a default value","service":"sms-backend","sql":"INSERT INTO users (email, password_hash, user_type, status) VALUES (?, ?, 'admin', 'active')","sqlMessage":"Field 'uuid' doesn't have a default value","sqlState":"HY000","stack":"Error: Field 'uuid' doesn't have a default value\n    at PromisePoolConnection.execute (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\promise\\connection.js:47:22)\n    at executeTransaction (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\config\\database.js:63:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async createAdmin (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\controllers\\adminController.js:215:5)","timestamp":"2025-07-23 15:32:16"}
{"code":"ER_NO_DEFAULT_FOR_FIELD","errno":1364,"level":"error","message":"Create admin error: Field 'uuid' doesn't have a default value","service":"sms-backend","sql":"INSERT INTO users (email, password_hash, user_type, status) VALUES (?, ?, 'admin', 'active')","sqlMessage":"Field 'uuid' doesn't have a default value","sqlState":"HY000","stack":"Error: Field 'uuid' doesn't have a default value\n    at PromisePoolConnection.execute (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\promise\\connection.js:47:22)\n    at executeTransaction (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\config\\database.js:63:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async createAdmin (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\controllers\\adminController.js:215:5)","timestamp":"2025-07-23 15:32:16"}
{"level":"info","message":"::ffff:127.0.0.1 - - [23/Jul/2025:14:32:16 +0000] \"POST /api/admin HTTP/1.1\" 500 57 \"-\" \"Thunder Client (https://www.thunderclient.com)\"","service":"sms-backend","timestamp":"2025-07-23 15:32:16"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-23 15:32:37"}
{"level":"info","message":"Database connected successfully","service":"sms-backend","timestamp":"2025-07-23 15:32:37"}
{"level":"info","message":"Server running on port 5000 in development mode","service":"sms-backend","timestamp":"2025-07-23 15:32:37"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-23 15:33:56"}
{"level":"info","message":"Database connected successfully","service":"sms-backend","timestamp":"2025-07-23 15:33:56"}
{"level":"info","message":"Server running on port 5000 in development mode","service":"sms-backend","timestamp":"2025-07-23 15:33:56"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-23 15:34:12"}
{"level":"info","message":"Database connected successfully","service":"sms-backend","timestamp":"2025-07-23 15:34:12"}
{"level":"info","message":"Server running on port 5000 in development mode","service":"sms-backend","timestamp":"2025-07-23 15:34:12"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-23 15:34:49"}
{"level":"info","message":"Database connected successfully","service":"sms-backend","timestamp":"2025-07-23 15:34:49"}
{"level":"info","message":"Server running on port 5000 in development mode","service":"sms-backend","timestamp":"2025-07-23 15:34:49"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-23 15:36:26"}
{"level":"info","message":"Database connected successfully","service":"sms-backend","timestamp":"2025-07-23 15:36:26"}
{"level":"info","message":"Server running on port 5000 in development mode","service":"sms-backend","timestamp":"2025-07-23 15:36:26"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-23 15:37:40"}
{"level":"info","message":"Database connected successfully","service":"sms-backend","timestamp":"2025-07-23 15:37:40"}
{"level":"info","message":"Server running on port 5000 in development mode","service":"sms-backend","timestamp":"2025-07-23 15:37:40"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-23 15:37:51"}
{"level":"info","message":"Database connected successfully","service":"sms-backend","timestamp":"2025-07-23 15:37:51"}
{"level":"info","message":"Server running on port 5000 in development mode","service":"sms-backend","timestamp":"2025-07-23 15:37:51"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-23 15:39:01"}
{"level":"info","message":"Database connected successfully","service":"sms-backend","timestamp":"2025-07-23 15:39:01"}
{"level":"info","message":"Server running on port 5000 in development mode","service":"sms-backend","timestamp":"2025-07-23 15:39:01"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-23 15:39:46"}
{"level":"info","message":"Database connected successfully","service":"sms-backend","timestamp":"2025-07-23 15:39:46"}
{"level":"info","message":"Server running on port 5000 in development mode","service":"sms-backend","timestamp":"2025-07-23 15:39:46"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-23 15:40:30"}
{"level":"info","message":"Database connected successfully","service":"sms-backend","timestamp":"2025-07-23 15:40:30"}
{"level":"info","message":"Server running on port 5000 in development mode","service":"sms-backend","timestamp":"2025-07-23 15:40:30"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-23 15:40:47"}
{"level":"info","message":"Database connected successfully","service":"sms-backend","timestamp":"2025-07-23 15:40:47"}
{"level":"info","message":"Server running on port 5000 in development mode","service":"sms-backend","timestamp":"2025-07-23 15:40:47"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-23 15:41:16"}
{"level":"info","message":"Database connected successfully","service":"sms-backend","timestamp":"2025-07-23 15:41:16"}
{"level":"info","message":"Server running on port 5000 in development mode","service":"sms-backend","timestamp":"2025-07-23 15:41:16"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-23 15:44:05"}
{"level":"info","message":"Database connected successfully","service":"sms-backend","timestamp":"2025-07-23 15:44:05"}
{"level":"info","message":"Server running on port 5000 in development mode","service":"sms-backend","timestamp":"2025-07-23 15:44:05"}
{"level":"error","message":"Authentication error: Invalid token","service":"sms-backend","stack":"Error: Invalid token\n    at verifyAccessToken (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\utils\\jwt.js:63:13)\n    at authenticate (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\middleware\\auth.js:22:21)\n    at newFn (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\index.js:47:12)","timestamp":"2025-07-23 15:44:08"}
{"level":"info","message":"::ffff:127.0.0.1 - - [23/Jul/2025:14:44:08 +0000] \"POST /api/admin HTTP/1.1\" 401 66 \"-\" \"Thunder Client (https://www.thunderclient.com)\"","service":"sms-backend","timestamp":"2025-07-23 15:44:08"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-23 15:44:22"}
{"level":"info","message":"Database connected successfully","service":"sms-backend","timestamp":"2025-07-23 15:44:22"}
{"level":"info","message":"Server running on port 5000 in development mode","service":"sms-backend","timestamp":"2025-07-23 15:44:22"}
{"code":"ER_BAD_NULL_ERROR","errno":1048,"level":"error","message":"Transaction error: Column 'role_id' cannot be null","service":"sms-backend","sql":"INSERT INTO user_roles (user_id, role_id) VALUES (LAST_INSERT_ID(), (SELECT id FROM roles WHERE name = ? LIMIT 1))","sqlMessage":"Column 'role_id' cannot be null","sqlState":"23000","stack":"Error: Column 'role_id' cannot be null\n    at PromisePoolConnection.execute (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\promise\\connection.js:47:22)\n    at executeTransaction (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\config\\database.js:63:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async createAdmin (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\controllers\\adminController.js:244:5)","timestamp":"2025-07-23 15:44:24"}
{"code":"ER_BAD_NULL_ERROR","errno":1048,"level":"error","message":"Create admin error: Column 'role_id' cannot be null","service":"sms-backend","sql":"INSERT INTO user_roles (user_id, role_id) VALUES (LAST_INSERT_ID(), (SELECT id FROM roles WHERE name = ? LIMIT 1))","sqlMessage":"Column 'role_id' cannot be null","sqlState":"23000","stack":"Error: Column 'role_id' cannot be null\n    at PromisePoolConnection.execute (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\promise\\connection.js:47:22)\n    at executeTransaction (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\config\\database.js:63:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async createAdmin (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\controllers\\adminController.js:244:5)","timestamp":"2025-07-23 15:44:24"}
{"level":"info","message":"::ffff:127.0.0.1 - - [23/Jul/2025:14:44:24 +0000] \"POST /api/admin HTTP/1.1\" 500 57 \"-\" \"Thunder Client (https://www.thunderclient.com)\"","service":"sms-backend","timestamp":"2025-07-23 15:44:24"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-23 15:45:29"}
{"level":"info","message":"Database connected successfully","service":"sms-backend","timestamp":"2025-07-23 15:45:29"}
{"level":"info","message":"Server running on port 5000 in development mode","service":"sms-backend","timestamp":"2025-07-23 15:45:29"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-23 15:45:47"}
{"level":"info","message":"Database connected successfully","service":"sms-backend","timestamp":"2025-07-23 15:45:47"}
{"level":"info","message":"Server running on port 5000 in development mode","service":"sms-backend","timestamp":"2025-07-23 15:45:47"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-23 15:46:05"}
{"level":"info","message":"Database connected successfully","service":"sms-backend","timestamp":"2025-07-23 15:46:05"}
{"level":"info","message":"Server running on port 5000 in development mode","service":"sms-backend","timestamp":"2025-07-23 15:46:05"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-23 15:46:17"}
{"level":"info","message":"Database connected successfully","service":"sms-backend","timestamp":"2025-07-23 15:46:17"}
{"level":"info","message":"Server running on port 5000 in development mode","service":"sms-backend","timestamp":"2025-07-23 15:46:17"}
{"level":"info","message":"::ffff:127.0.0.1 - - [23/Jul/2025:14:47:38 +0000] \"POST /api/admin HTTP/1.1\" 400 160 \"-\" \"Thunder Client (https://www.thunderclient.com)\"","service":"sms-backend","timestamp":"2025-07-23 15:47:38"}
{"level":"info","message":"Admin user created: <EMAIL> by user system","service":"sms-backend","timestamp":"2025-07-23 15:48:01"}
{"level":"info","message":"::ffff:127.0.0.1 - - [23/Jul/2025:14:48:01 +0000] \"POST /api/admin HTTP/1.1\" 201 208 \"-\" \"Thunder Client (https://www.thunderclient.com)\"","service":"sms-backend","timestamp":"2025-07-23 15:48:01"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-23 15:48:12"}
{"level":"info","message":"Database connected successfully","service":"sms-backend","timestamp":"2025-07-23 15:48:12"}
{"level":"info","message":"Server running on port 5000 in development mode","service":"sms-backend","timestamp":"2025-07-23 15:48:12"}
{"level":"error","message":"Authentication error: Invalid token","service":"sms-backend","stack":"Error: Invalid token\n    at verifyAccessToken (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\utils\\jwt.js:63:13)\n    at authenticate (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\middleware\\auth.js:22:21)\n    at newFn (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\index.js:47:12)","timestamp":"2025-07-23 15:48:23"}
{"level":"info","message":"::ffff:127.0.0.1 - - [23/Jul/2025:14:48:23 +0000] \"POST /api/admin HTTP/1.1\" 401 66 \"-\" \"Thunder Client (https://www.thunderclient.com)\"","service":"sms-backend","timestamp":"2025-07-23 15:48:23"}
{"error":"Table 'school_management_system.admins' doesn't exist","level":"error","message":"Database query error:","params":[2],"query":"SELECT * FROM admins WHERE user_id = ?","service":"sms-backend","timestamp":"2025-07-23 15:48:42"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Login error: Table 'school_management_system.admins' doesn't exist","service":"sms-backend","sql":"SELECT * FROM admins WHERE user_id = ?","sqlMessage":"Table 'school_management_system.admins' doesn't exist","sqlState":"42S02","stack":"Error: Table 'school_management_system.admins' doesn't exist\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\config\\database.js:48:31)\n    at login (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\controllers\\authController.js:87:30)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-23 15:48:42"}
{"level":"info","message":"::1 - - [23/Jul/2025:14:48:42 +0000] \"POST /api/auth/login HTTP/1.1\" 500 42 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-23 15:48:42"}
{"error":"Table 'school_management_system.admins' doesn't exist","level":"error","message":"Database query error:","params":[2],"query":"SELECT * FROM admins WHERE user_id = ?","service":"sms-backend","timestamp":"2025-07-23 15:48:46"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Login error: Table 'school_management_system.admins' doesn't exist","service":"sms-backend","sql":"SELECT * FROM admins WHERE user_id = ?","sqlMessage":"Table 'school_management_system.admins' doesn't exist","sqlState":"42S02","stack":"Error: Table 'school_management_system.admins' doesn't exist\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\config\\database.js:48:31)\n    at login (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\controllers\\authController.js:87:30)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-23 15:48:46"}
{"level":"info","message":"::1 - - [23/Jul/2025:14:48:46 +0000] \"POST /api/auth/login HTTP/1.1\" 500 42 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-23 15:48:46"}
{"level":"info","message":"::1 - - [23/Jul/2025:14:48:51 +0000] \"POST /api/auth/login HTTP/1.1\" 401 55 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-23 15:48:51"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-23 15:49:16"}
{"level":"info","message":"Database connected successfully","service":"sms-backend","timestamp":"2025-07-23 15:49:16"}
{"level":"info","message":"Server running on port 5000 in development mode","service":"sms-backend","timestamp":"2025-07-23 15:49:16"}
{"error":"Table 'school_management_system.admins' doesn't exist","level":"error","message":"Database query error:","params":[2],"query":"SELECT * FROM admins WHERE user_id = ?","service":"sms-backend","timestamp":"2025-07-23 15:49:31"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Login error: Table 'school_management_system.admins' doesn't exist","service":"sms-backend","sql":"SELECT * FROM admins WHERE user_id = ?","sqlMessage":"Table 'school_management_system.admins' doesn't exist","sqlState":"42S02","stack":"Error: Table 'school_management_system.admins' doesn't exist\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\config\\database.js:48:31)\n    at login (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\controllers\\authController.js:87:30)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-23 15:49:31"}
{"level":"info","message":"::1 - - [23/Jul/2025:14:49:31 +0000] \"POST /api/auth/login HTTP/1.1\" 500 42 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-23 15:49:31"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-23 15:52:45"}
{"level":"info","message":"Database connected successfully","service":"sms-backend","timestamp":"2025-07-23 15:52:45"}
{"level":"info","message":"Server running on port 5000 in development mode","service":"sms-backend","timestamp":"2025-07-23 15:52:45"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-23 15:53:30"}
{"level":"info","message":"Database connected successfully","service":"sms-backend","timestamp":"2025-07-23 15:53:30"}
{"level":"info","message":"Server running on port 5000 in development mode","service":"sms-backend","timestamp":"2025-07-23 15:53:30"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-23 15:53:45"}
{"level":"info","message":"Database connected successfully","service":"sms-backend","timestamp":"2025-07-23 15:53:45"}
{"level":"info","message":"Server running on port 5000 in development mode","service":"sms-backend","timestamp":"2025-07-23 15:53:45"}
{"level":"info","message":"User <EMAIL> logged in successfully","service":"sms-backend","timestamp":"2025-07-23 15:53:54"}
{"level":"info","message":"::1 - - [23/Jul/2025:14:53:54 +0000] \"POST /api/auth/login HTTP/1.1\" 200 846 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-23 15:53:54"}
{"error":"Unknown column 'date' in 'where clause'","level":"error","message":"Database query error:","params":["2025-07-23"],"query":"\n      SELECT\n        COUNT(*) as total_records,\n        SUM(CASE WHEN status = 'present' THEN 1 ELSE 0 END) as present_count,\n        SUM(CASE WHEN status = 'absent' THEN 1 ELSE 0 END) as absent_count,\n        SUM(CASE WHEN status = 'late' THEN 1 ELSE 0 END) as late_count\n      FROM attendance\n      WHERE date = ?\n    ","service":"sms-backend","timestamp":"2025-07-23 15:55:33"}
{"level":"info","message":"::1 - - [23/Jul/2025:14:55:33 +0000] \"GET /api/analytics/dashboard HTTP/1.1\" 500 113 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-23 15:55:33"}
{"error":"Unknown column 'date' in 'where clause'","level":"error","message":"Database query error:","params":["2025-07-23"],"query":"\n      SELECT\n        COUNT(*) as total_records,\n        SUM(CASE WHEN status = 'present' THEN 1 ELSE 0 END) as present_count,\n        SUM(CASE WHEN status = 'absent' THEN 1 ELSE 0 END) as absent_count,\n        SUM(CASE WHEN status = 'late' THEN 1 ELSE 0 END) as late_count\n      FROM attendance\n      WHERE date = ?\n    ","service":"sms-backend","timestamp":"2025-07-23 15:55:33"}
{"level":"info","message":"::1 - - [23/Jul/2025:14:55:33 +0000] \"GET /api/analytics/dashboard HTTP/1.1\" 500 113 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-23 15:55:33"}
{"error":"Unknown column 'date' in 'where clause'","level":"error","message":"Database query error:","params":["2025-07-23"],"query":"\n      SELECT\n        COUNT(*) as total_records,\n        SUM(CASE WHEN status = 'present' THEN 1 ELSE 0 END) as present_count,\n        SUM(CASE WHEN status = 'absent' THEN 1 ELSE 0 END) as absent_count,\n        SUM(CASE WHEN status = 'late' THEN 1 ELSE 0 END) as late_count\n      FROM attendance\n      WHERE date = ?\n    ","service":"sms-backend","timestamp":"2025-07-23 15:56:21"}
{"level":"info","message":"::1 - - [23/Jul/2025:14:56:21 +0000] \"GET /api/analytics/dashboard HTTP/1.1\" 500 113 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-23 15:56:21"}
{"error":"Table 'school_management_system.admins' doesn't exist","level":"error","message":"Database query error:","params":[2],"query":"SELECT * FROM admins WHERE user_id = ?","service":"sms-backend","timestamp":"2025-07-23 15:56:45"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Get profile error: Table 'school_management_system.admins' doesn't exist","service":"sms-backend","sql":"SELECT * FROM admins WHERE user_id = ?","sqlMessage":"Table 'school_management_system.admins' doesn't exist","sqlState":"42S02","stack":"Error: Table 'school_management_system.admins' doesn't exist\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\config\\database.js:48:31)\n    at getProfile (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\controllers\\authController.js:255:30)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-23 15:56:45"}
{"level":"info","message":"::1 - - [23/Jul/2025:14:56:45 +0000] \"GET /api/auth/profile HTTP/1.1\" 500 51 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-23 15:56:45"}
{"level":"info","message":"User <EMAIL> logged in successfully","service":"sms-backend","timestamp":"2025-07-23 16:03:53"}
{"level":"info","message":"::1 - - [23/Jul/2025:15:03:53 +0000] \"POST /api/auth/login HTTP/1.1\" 200 846 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-23 16:03:53"}
{"error":"Unknown column 'date' in 'where clause'","level":"error","message":"Database query error:","params":["2025-07-23"],"query":"\n      SELECT\n        COUNT(*) as total_records,\n        SUM(CASE WHEN status = 'present' THEN 1 ELSE 0 END) as present_count,\n        SUM(CASE WHEN status = 'absent' THEN 1 ELSE 0 END) as absent_count,\n        SUM(CASE WHEN status = 'late' THEN 1 ELSE 0 END) as late_count\n      FROM attendance\n      WHERE date = ?\n    ","service":"sms-backend","timestamp":"2025-07-23 16:03:53"}
{"level":"info","message":"::1 - - [23/Jul/2025:15:03:53 +0000] \"GET /api/analytics/dashboard HTTP/1.1\" 500 113 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-23 16:03:53"}
{"error":"Unknown column 'date' in 'where clause'","level":"error","message":"Database query error:","params":["2025-07-23"],"query":"\n      SELECT\n        COUNT(*) as total_records,\n        SUM(CASE WHEN status = 'present' THEN 1 ELSE 0 END) as present_count,\n        SUM(CASE WHEN status = 'absent' THEN 1 ELSE 0 END) as absent_count,\n        SUM(CASE WHEN status = 'late' THEN 1 ELSE 0 END) as late_count\n      FROM attendance\n      WHERE date = ?\n    ","service":"sms-backend","timestamp":"2025-07-23 16:03:53"}
{"level":"info","message":"::1 - - [23/Jul/2025:15:03:53 +0000] \"GET /api/analytics/dashboard HTTP/1.1\" 500 113 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-23 16:03:53"}
{"error":"Table 'school_management_system.admins' doesn't exist","level":"error","message":"Database query error:","params":[2],"query":"SELECT * FROM admins WHERE user_id = ?","service":"sms-backend","timestamp":"2025-07-23 16:04:17"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Get profile error: Table 'school_management_system.admins' doesn't exist","service":"sms-backend","sql":"SELECT * FROM admins WHERE user_id = ?","sqlMessage":"Table 'school_management_system.admins' doesn't exist","sqlState":"42S02","stack":"Error: Table 'school_management_system.admins' doesn't exist\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\config\\database.js:48:31)\n    at getProfile (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\controllers\\authController.js:255:30)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-23 16:04:17"}
{"level":"info","message":"::1 - - [23/Jul/2025:15:04:17 +0000] \"GET /api/auth/profile HTTP/1.1\" 500 51 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-23 16:04:17"}
{"level":"info","message":"User <EMAIL> logged in successfully","service":"sms-backend","timestamp":"2025-07-23 16:23:39"}
{"level":"info","message":"::1 - - [23/Jul/2025:15:23:39 +0000] \"POST /api/auth/login HTTP/1.1\" 200 846 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-23 16:23:39"}
{"error":"Unknown column 'date' in 'where clause'","level":"error","message":"Database query error:","params":["2025-07-23"],"query":"\n      SELECT\n        COUNT(*) as total_records,\n        SUM(CASE WHEN status = 'present' THEN 1 ELSE 0 END) as present_count,\n        SUM(CASE WHEN status = 'absent' THEN 1 ELSE 0 END) as absent_count,\n        SUM(CASE WHEN status = 'late' THEN 1 ELSE 0 END) as late_count\n      FROM attendance\n      WHERE date = ?\n    ","service":"sms-backend","timestamp":"2025-07-23 16:23:39"}
{"level":"info","message":"::1 - - [23/Jul/2025:15:23:39 +0000] \"GET /api/analytics/dashboard HTTP/1.1\" 500 113 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-23 16:23:39"}
{"error":"Unknown column 'date' in 'where clause'","level":"error","message":"Database query error:","params":["2025-07-23"],"query":"\n      SELECT\n        COUNT(*) as total_records,\n        SUM(CASE WHEN status = 'present' THEN 1 ELSE 0 END) as present_count,\n        SUM(CASE WHEN status = 'absent' THEN 1 ELSE 0 END) as absent_count,\n        SUM(CASE WHEN status = 'late' THEN 1 ELSE 0 END) as late_count\n      FROM attendance\n      WHERE date = ?\n    ","service":"sms-backend","timestamp":"2025-07-23 16:23:39"}
{"level":"info","message":"::1 - - [23/Jul/2025:15:23:39 +0000] \"GET /api/analytics/dashboard HTTP/1.1\" 500 113 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-23 16:23:39"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-23 16:25:42"}
{"level":"info","message":"Database connected successfully","service":"sms-backend","timestamp":"2025-07-23 16:25:42"}
{"level":"info","message":"Server running on port 5000 in development mode","service":"sms-backend","timestamp":"2025-07-23 16:25:42"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-23 16:26:06"}
{"level":"info","message":"Database connected successfully","service":"sms-backend","timestamp":"2025-07-23 16:26:06"}
{"level":"info","message":"Server running on port 5000 in development mode","service":"sms-backend","timestamp":"2025-07-23 16:26:06"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-23 16:27:41"}
{"level":"info","message":"Database connected successfully","service":"sms-backend","timestamp":"2025-07-23 16:27:41"}
{"level":"info","message":"Server running on port 5000 in development mode","service":"sms-backend","timestamp":"2025-07-23 16:27:41"}
{"error":"Table 'school_management_system.admins' doesn't exist","level":"error","message":"Database query error:","params":[2],"query":"SELECT * FROM admins WHERE user_id = ?","service":"sms-backend","timestamp":"2025-07-23 16:44:35"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Get profile error: Table 'school_management_system.admins' doesn't exist","service":"sms-backend","sql":"SELECT * FROM admins WHERE user_id = ?","sqlMessage":"Table 'school_management_system.admins' doesn't exist","sqlState":"42S02","stack":"Error: Table 'school_management_system.admins' doesn't exist\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\config\\database.js:48:31)\n    at getProfile (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\controllers\\authController.js:255:30)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-23 16:44:35"}
{"level":"info","message":"::1 - - [23/Jul/2025:15:44:35 +0000] \"GET /api/auth/profile HTTP/1.1\" 500 51 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-23 16:44:35"}
{"level":"info","message":"User <EMAIL> logged in successfully","service":"sms-backend","timestamp":"2025-07-23 16:44:46"}
{"level":"info","message":"::1 - - [23/Jul/2025:15:44:46 +0000] \"POST /api/auth/login HTTP/1.1\" 200 846 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-23 16:44:46"}
{"error":"Unknown column 'date' in 'where clause'","level":"error","message":"Database query error:","params":["2025-07-23"],"query":"\n        SELECT\n          COUNT(*) as total_records,\n          SUM(CASE WHEN status = 'present' THEN 1 ELSE 0 END) as present_count,\n          SUM(CASE WHEN status = 'absent' THEN 1 ELSE 0 END) as absent_count,\n          SUM(CASE WHEN status = 'late' THEN 1 ELSE 0 END) as late_count\n        FROM attendance\n        WHERE date = ?\n      ","service":"sms-backend","timestamp":"2025-07-23 16:44:46"}
{"error":"Unknown column 'type' in 'field list'","level":"error","message":"Database query error:","params":[],"query":"\n        SELECT title, start_date, start_time, type, location\n        FROM events\n        WHERE start_date >= CURDATE() AND status = 'active'\n        ORDER BY start_date ASC, start_time ASC\n        LIMIT 5\n      ","service":"sms-backend","timestamp":"2025-07-23 16:44:46"}
{"level":"info","message":"::1 - - [23/Jul/2025:15:44:46 +0000] \"GET /api/analytics/dashboard HTTP/1.1\" 200 367 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-23 16:44:46"}
{"error":"Unknown column 'date' in 'where clause'","level":"error","message":"Database query error:","params":["2025-07-23"],"query":"\n        SELECT\n          COUNT(*) as total_records,\n          SUM(CASE WHEN status = 'present' THEN 1 ELSE 0 END) as present_count,\n          SUM(CASE WHEN status = 'absent' THEN 1 ELSE 0 END) as absent_count,\n          SUM(CASE WHEN status = 'late' THEN 1 ELSE 0 END) as late_count\n        FROM attendance\n        WHERE date = ?\n      ","service":"sms-backend","timestamp":"2025-07-23 16:44:46"}
{"error":"Unknown column 'type' in 'field list'","level":"error","message":"Database query error:","params":[],"query":"\n        SELECT title, start_date, start_time, type, location\n        FROM events\n        WHERE start_date >= CURDATE() AND status = 'active'\n        ORDER BY start_date ASC, start_time ASC\n        LIMIT 5\n      ","service":"sms-backend","timestamp":"2025-07-23 16:44:46"}
{"level":"info","message":"::1 - - [23/Jul/2025:15:44:46 +0000] \"GET /api/analytics/dashboard HTTP/1.1\" 304 - \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-23 16:44:46"}
{"level":"info","message":"::1 - - [23/Jul/2025:15:45:03 +0000] \"GET /api/students?page=1&limit=10&search=&sort_by=first_name&sort_order=asc HTTP/1.1\" 400 138 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-23 16:45:03"}
{"level":"info","message":"::1 - - [23/Jul/2025:15:45:03 +0000] \"GET /api/students?page=1&limit=10&search=&sort_by=first_name&sort_order=asc HTTP/1.1\" 400 138 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-23 16:45:03"}
{"level":"info","message":"::1 - - [23/Jul/2025:15:46:49 +0000] \"POST /api/students HTTP/1.1\" 400 655 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-23 16:46:49"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-23 16:50:26"}
{"level":"info","message":"Database connected successfully","service":"sms-backend","timestamp":"2025-07-23 16:50:26"}
{"level":"info","message":"Server running on port 5000 in development mode","service":"sms-backend","timestamp":"2025-07-23 16:50:26"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-23 16:50:54"}
{"level":"info","message":"Database connected successfully","service":"sms-backend","timestamp":"2025-07-23 16:50:54"}
{"level":"info","message":"Server running on port 5000 in development mode","service":"sms-backend","timestamp":"2025-07-23 16:50:54"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-23 16:51:11"}
{"level":"info","message":"Database connected successfully","service":"sms-backend","timestamp":"2025-07-23 16:51:11"}
{"level":"info","message":"Server running on port 5000 in development mode","service":"sms-backend","timestamp":"2025-07-23 16:51:11"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-23 16:52:13"}
{"level":"info","message":"Database connected successfully","service":"sms-backend","timestamp":"2025-07-23 16:52:13"}
{"level":"info","message":"Server running on port 5000 in development mode","service":"sms-backend","timestamp":"2025-07-23 16:52:13"}
{"level":"info","message":"::1 - - [23/Jul/2025:16:02:20 +0000] \"POST /api/students HTTP/1.1\" 400 655 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-23 17:02:20"}
{"error":"Table 'school_management_system.admins' doesn't exist","level":"error","message":"Database query error:","params":[2],"query":"SELECT * FROM admins WHERE user_id = ?","service":"sms-backend","timestamp":"2025-07-23 17:03:05"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Get profile error: Table 'school_management_system.admins' doesn't exist","service":"sms-backend","sql":"SELECT * FROM admins WHERE user_id = ?","sqlMessage":"Table 'school_management_system.admins' doesn't exist","sqlState":"42S02","stack":"Error: Table 'school_management_system.admins' doesn't exist\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\config\\database.js:48:31)\n    at getProfile (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\controllers\\authController.js:255:30)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-23 17:03:05"}
{"level":"info","message":"::1 - - [23/Jul/2025:16:03:05 +0000] \"GET /api/auth/profile HTTP/1.1\" 500 51 \"http://localhost:3000/\" \"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-23 17:03:05"}
{"level":"info","message":"User <EMAIL> logged in successfully","service":"sms-backend","timestamp":"2025-07-23 17:03:17"}
{"level":"info","message":"::1 - - [23/Jul/2025:16:03:17 +0000] \"POST /api/auth/login HTTP/1.1\" 200 846 \"http://localhost:3000/\" \"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-23 17:03:17"}
{"error":"Unknown column 'date' in 'where clause'","level":"error","message":"Database query error:","params":["2025-07-23"],"query":"\n        SELECT\n          COUNT(*) as total_records,\n          SUM(CASE WHEN status = 'present' THEN 1 ELSE 0 END) as present_count,\n          SUM(CASE WHEN status = 'absent' THEN 1 ELSE 0 END) as absent_count,\n          SUM(CASE WHEN status = 'late' THEN 1 ELSE 0 END) as late_count\n        FROM attendance\n        WHERE date = ?\n      ","service":"sms-backend","timestamp":"2025-07-23 17:03:18"}
{"error":"Unknown column 'type' in 'field list'","level":"error","message":"Database query error:","params":[],"query":"\n        SELECT title, start_date, start_time, type, location\n        FROM events\n        WHERE start_date >= CURDATE() AND status = 'active'\n        ORDER BY start_date ASC, start_time ASC\n        LIMIT 5\n      ","service":"sms-backend","timestamp":"2025-07-23 17:03:18"}
{"level":"info","message":"::1 - - [23/Jul/2025:16:03:18 +0000] \"GET /api/analytics/dashboard HTTP/1.1\" 304 - \"http://localhost:3000/\" \"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-23 17:03:18"}
{"error":"Unknown column 'date' in 'where clause'","level":"error","message":"Database query error:","params":["2025-07-23"],"query":"\n        SELECT\n          COUNT(*) as total_records,\n          SUM(CASE WHEN status = 'present' THEN 1 ELSE 0 END) as present_count,\n          SUM(CASE WHEN status = 'absent' THEN 1 ELSE 0 END) as absent_count,\n          SUM(CASE WHEN status = 'late' THEN 1 ELSE 0 END) as late_count\n        FROM attendance\n        WHERE date = ?\n      ","service":"sms-backend","timestamp":"2025-07-23 17:03:18"}
{"error":"Unknown column 'type' in 'field list'","level":"error","message":"Database query error:","params":[],"query":"\n        SELECT title, start_date, start_time, type, location\n        FROM events\n        WHERE start_date >= CURDATE() AND status = 'active'\n        ORDER BY start_date ASC, start_time ASC\n        LIMIT 5\n      ","service":"sms-backend","timestamp":"2025-07-23 17:03:18"}
{"level":"info","message":"::1 - - [23/Jul/2025:16:03:18 +0000] \"GET /api/analytics/dashboard HTTP/1.1\" 304 - \"http://localhost:3000/\" \"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-23 17:03:18"}
{"error":"Table 'school_management_system.admins' doesn't exist","level":"error","message":"Database query error:","params":[2],"query":"SELECT * FROM admins WHERE user_id = ?","service":"sms-backend","timestamp":"2025-07-23 17:03:22"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Get profile error: Table 'school_management_system.admins' doesn't exist","service":"sms-backend","sql":"SELECT * FROM admins WHERE user_id = ?","sqlMessage":"Table 'school_management_system.admins' doesn't exist","sqlState":"42S02","stack":"Error: Table 'school_management_system.admins' doesn't exist\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\config\\database.js:48:31)\n    at getProfile (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\controllers\\authController.js:255:30)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-23 17:03:22"}
{"level":"info","message":"::1 - - [23/Jul/2025:16:03:22 +0000] \"GET /api/auth/profile HTTP/1.1\" 500 51 \"http://localhost:3000/\" \"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-23 17:03:22"}
{"level":"info","message":"SIGINT received. Shutting down gracefully...","service":"sms-backend","timestamp":"2025-07-23 17:24:07"}
{"code":"ER_ACCESS_DENIED_ERROR","errno":1045,"level":"error","message":"Database connection failed: Access denied for user 'root'@'localhost' (using password: YES)","service":"sms-backend","sqlMessage":"Access denied for user 'root'@'localhost' (using password: YES)","sqlState":"28000","stack":"Error: Access denied for user 'root'@'localhost' (using password: YES)\n    at Packet.asError (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\packets\\packet.js:740:17)\n    at ClientHandshake.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\commands\\command.js:29:26)\n    at PoolConnection.handlePacket (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\base\\connection.js:475:34)\n    at PacketParser.onPacket (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\base\\connection.js:93:12)\n    at PacketParser.executeStart (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\packet_parser.js:75:16)\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\base\\connection.js:100:25)\n    at Socket.emit (node:events:519:28)\n    at addChunk (node:internal/streams/readable:559:12)\n    at readableAddChunkPushByteMode (node:internal/streams/readable:510:3)\n    at Readable.push (node:internal/streams/readable:390:5)","timestamp":"2025-07-26 02:06:27"}
{"code":"ER_ACCESS_DENIED_ERROR","errno":1045,"level":"error","message":"Database connection failed: Access denied for user 'root'@'localhost' (using password: YES)","service":"sms-backend","sqlMessage":"Access denied for user 'root'@'localhost' (using password: YES)","sqlState":"28000","stack":"Error: Access denied for user 'root'@'localhost' (using password: YES)\n    at Packet.asError (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\packets\\packet.js:740:17)\n    at ClientHandshake.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\commands\\command.js:29:26)\n    at PoolConnection.handlePacket (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\base\\connection.js:475:34)\n    at PacketParser.onPacket (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\base\\connection.js:93:12)\n    at PacketParser.executeStart (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\packet_parser.js:75:16)\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\base\\connection.js:100:25)\n    at Socket.emit (node:events:519:28)\n    at addChunk (node:internal/streams/readable:559:12)\n    at readableAddChunkPushByteMode (node:internal/streams/readable:510:3)\n    at Readable.push (node:internal/streams/readable:390:5)","timestamp":"2025-07-26 02:09:41"}
{"code":"ER_ACCESS_DENIED_ERROR","errno":1045,"level":"error","message":"Database connection failed: Access denied for user 'root'@'localhost' (using password: YES)","service":"sms-backend","sqlMessage":"Access denied for user 'root'@'localhost' (using password: YES)","sqlState":"28000","stack":"Error: Access denied for user 'root'@'localhost' (using password: YES)\n    at Packet.asError (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\packets\\packet.js:740:17)\n    at ClientHandshake.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\commands\\command.js:29:26)\n    at PoolConnection.handlePacket (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\base\\connection.js:475:34)\n    at PacketParser.onPacket (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\base\\connection.js:93:12)\n    at PacketParser.executeStart (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\packet_parser.js:75:16)\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\base\\connection.js:100:25)\n    at Socket.emit (node:events:519:28)\n    at addChunk (node:internal/streams/readable:559:12)\n    at readableAddChunkPushByteMode (node:internal/streams/readable:510:3)\n    at Readable.push (node:internal/streams/readable:390:5)","timestamp":"2025-07-26 02:11:27"}
{"code":"ER_ACCESS_DENIED_ERROR","errno":1045,"level":"error","message":"Database connection failed: Access denied for user 'root'@'localhost' (using password: YES)","service":"sms-backend","sqlMessage":"Access denied for user 'root'@'localhost' (using password: YES)","sqlState":"28000","stack":"Error: Access denied for user 'root'@'localhost' (using password: YES)\n    at Packet.asError (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\packets\\packet.js:740:17)\n    at ClientHandshake.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\commands\\command.js:29:26)\n    at PoolConnection.handlePacket (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\base\\connection.js:475:34)\n    at PacketParser.onPacket (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\base\\connection.js:93:12)\n    at PacketParser.executeStart (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\packet_parser.js:75:16)\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\base\\connection.js:100:25)\n    at Socket.emit (node:events:519:28)\n    at addChunk (node:internal/streams/readable:559:12)\n    at readableAddChunkPushByteMode (node:internal/streams/readable:510:3)\n    at Readable.push (node:internal/streams/readable:390:5)","timestamp":"2025-07-26 02:11:58"}
{"code":"ER_BAD_DB_ERROR","errno":1049,"level":"error","message":"Database connection failed: Unknown database 'school_management_system'","service":"sms-backend","sqlMessage":"Unknown database 'school_management_system'","sqlState":"42000","stack":"Error: Unknown database 'school_management_system'\n    at Packet.asError (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\packets\\packet.js:740:17)\n    at ClientHandshake.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\commands\\command.js:29:26)\n    at PoolConnection.handlePacket (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\base\\connection.js:475:34)\n    at PacketParser.onPacket (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\base\\connection.js:93:12)\n    at PacketParser.executeStart (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\packet_parser.js:75:16)\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\base\\connection.js:100:25)\n    at Socket.emit (node:events:519:28)\n    at addChunk (node:internal/streams/readable:559:12)\n    at readableAddChunkPushByteMode (node:internal/streams/readable:510:3)\n    at Readable.push (node:internal/streams/readable:390:5)","timestamp":"2025-07-26 02:14:12"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-26 02:19:25"}
{"level":"info","message":"Database connected successfully","service":"sms-backend","timestamp":"2025-07-26 02:19:25"}
{"level":"info","message":"Server running on port 5000 in undefined mode","service":"sms-backend","timestamp":"2025-07-26 02:19:25"}
{"level":"info","message":"SIGINT received. Shutting down gracefully...","service":"sms-backend","timestamp":"2025-07-26 02:19:31"}
{"level":"info","message":"Starting database migration process...","service":"sms-backend","timestamp":"2025-07-26 02:19:49"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-26 02:19:49"}
{"level":"info","message":"Database connection established","service":"sms-backend","timestamp":"2025-07-26 02:19:49"}
{"level":"info","message":"Migrations table created or already exists","service":"sms-backend","timestamp":"2025-07-26 02:19:50"}
{"level":"info","message":"Found 7 pending migrations","service":"sms-backend","timestamp":"2025-07-26 02:19:50"}
{"level":"info","message":"Executing migration: 001_create_users_and_auth_tables.js","service":"sms-backend","timestamp":"2025-07-26 02:19:50"}
{"level":"info","message":"Creating users and authentication tables...","service":"sms-backend","timestamp":"2025-07-26 02:19:50"}
{"level":"info","message":"Users and authentication tables created successfully","service":"sms-backend","timestamp":"2025-07-26 02:19:50"}
{"level":"info","message":"Migration completed: 001_create_users_and_auth_tables.js","service":"sms-backend","timestamp":"2025-07-26 02:19:50"}
{"level":"info","message":"Executing migration: 002_create_academic_structure_tables.js","service":"sms-backend","timestamp":"2025-07-26 02:19:50"}
{"level":"info","message":"Creating academic structure tables...","service":"sms-backend","timestamp":"2025-07-26 02:19:50"}
{"level":"info","message":"Academic structure tables created successfully","service":"sms-backend","timestamp":"2025-07-26 02:19:51"}
{"level":"info","message":"Migration completed: 002_create_academic_structure_tables.js","service":"sms-backend","timestamp":"2025-07-26 02:19:51"}
{"level":"info","message":"Executing migration: 003_create_assessment_and_grading_tables.js","service":"sms-backend","timestamp":"2025-07-26 02:19:51"}
{"level":"info","message":"Creating assessment and grading tables...","service":"sms-backend","timestamp":"2025-07-26 02:19:51"}
{"level":"info","message":"Assessment and grading tables created successfully","service":"sms-backend","timestamp":"2025-07-26 02:19:51"}
{"level":"info","message":"Migration completed: 003_create_assessment_and_grading_tables.js","service":"sms-backend","timestamp":"2025-07-26 02:19:51"}
{"level":"info","message":"Executing migration: 004_create_attendance_and_timetable_tables.js","service":"sms-backend","timestamp":"2025-07-26 02:19:51"}
{"level":"info","message":"Creating attendance and timetable tables...","service":"sms-backend","timestamp":"2025-07-26 02:19:51"}
{"level":"info","message":"Attendance and timetable tables created successfully","service":"sms-backend","timestamp":"2025-07-26 02:19:52"}
{"level":"info","message":"Migration completed: 004_create_attendance_and_timetable_tables.js","service":"sms-backend","timestamp":"2025-07-26 02:19:52"}
{"level":"info","message":"Executing migration: 005_create_health_and_library_tables.js","service":"sms-backend","timestamp":"2025-07-26 02:19:52"}
{"level":"info","message":"Creating health records and library tables...","service":"sms-backend","timestamp":"2025-07-26 02:19:52"}
{"level":"info","message":"Health records and library tables created successfully","service":"sms-backend","timestamp":"2025-07-26 02:19:53"}
{"level":"info","message":"Migration completed: 005_create_health_and_library_tables.js","service":"sms-backend","timestamp":"2025-07-26 02:19:53"}
{"level":"info","message":"Executing migration: 006_create_transportation_and_communication_tables.js","service":"sms-backend","timestamp":"2025-07-26 02:19:53"}
{"level":"info","message":"Creating transportation and communication tables...","service":"sms-backend","timestamp":"2025-07-26 02:19:53"}
{"level":"info","message":"Transportation and communication tables created successfully","service":"sms-backend","timestamp":"2025-07-26 02:19:54"}
{"level":"info","message":"Migration completed: 006_create_transportation_and_communication_tables.js","service":"sms-backend","timestamp":"2025-07-26 02:19:54"}
{"level":"info","message":"Executing migration: 007_create_fees_and_admin_tables.js","service":"sms-backend","timestamp":"2025-07-26 02:19:54"}
{"level":"info","message":"Creating fees and administrative tables...","service":"sms-backend","timestamp":"2025-07-26 02:19:54"}
{"level":"info","message":"Fees and administrative tables created successfully","service":"sms-backend","timestamp":"2025-07-26 02:19:55"}
{"level":"info","message":"Migration completed: 007_create_fees_and_admin_tables.js","service":"sms-backend","timestamp":"2025-07-26 02:19:55"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-26 02:20:32"}
{"level":"info","message":"Database connected successfully","service":"sms-backend","timestamp":"2025-07-26 02:20:32"}
{"level":"info","message":"Server running on port 5000 in undefined mode","service":"sms-backend","timestamp":"2025-07-26 02:20:32"}
{"level":"info","message":"::1 - - [26/Jul/2025:13:40:04 +0000] \"POST /api/auth/login HTTP/1.1\" 401 55 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-26 02:40:04"}
{"level":"info","message":"::1 - - [26/Jul/2025:13:40:34 +0000] \"POST /api/auth/login HTTP/1.1\" 401 55 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-26 02:40:34"}
{"level":"info","message":"::ffff:127.0.0.1 - - [26/Jul/2025:13:44:47 +0000] \"GET /api/admin HTTP/1.1\" 401 51 \"-\" \"Thunder Client (https://www.thunderclient.com)\"","service":"sms-backend","timestamp":"2025-07-26 02:44:47"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-26 02:45:15"}
{"level":"info","message":"Database connected successfully","service":"sms-backend","timestamp":"2025-07-26 02:45:15"}
{"level":"info","message":"Server running on port 5000 in undefined mode","service":"sms-backend","timestamp":"2025-07-26 02:45:15"}
{"error":"Incorrect arguments to mysqld_stmt_execute","level":"error","message":"Database query error:","params":[20,0],"query":"\n      SELECT \n        u.id,\n        u.uuid,\n        u.first_name,\n        u.last_name,\n        u.email,\n        u.phone,\n        u.status,\n        u.last_login,\n        u.created_at,\n        GROUP_CONCAT(r.name) as roles,\n        CONCAT(u.first_name, ' ', u.last_name) as full_name\n      FROM users u\n      LEFT JOIN user_roles ur ON u.id = ur.user_id\n      LEFT JOIN roles r ON ur.role_id = r.id\n      WHERE u.user_type = 'admin'\n      GROUP BY u.id\n      ORDER BY u.created_at DESC\n      LIMIT ? OFFSET ?\n    ","service":"sms-backend","timestamp":"2025-07-26 02:45:16"}
{"code":"ER_WRONG_ARGUMENTS","errno":1210,"level":"error","message":"Get admins error: Incorrect arguments to mysqld_stmt_execute","service":"sms-backend","sql":"\n      SELECT \n        u.id,\n        u.uuid,\n        u.first_name,\n        u.last_name,\n        u.email,\n        u.phone,\n        u.status,\n        u.last_login,\n        u.created_at,\n        GROUP_CONCAT(r.name) as roles,\n        CONCAT(u.first_name, ' ', u.last_name) as full_name\n      FROM users u\n      LEFT JOIN user_roles ur ON u.id = ur.user_id\n      LEFT JOIN roles r ON ur.role_id = r.id\n      WHERE u.user_type = 'admin'\n      GROUP BY u.id\n      ORDER BY u.created_at DESC\n      LIMIT ? OFFSET ?\n    ","sqlMessage":"Incorrect arguments to mysqld_stmt_execute","sqlState":"HY000","stack":"Error: Incorrect arguments to mysqld_stmt_execute\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\config\\database.js:48:31)\n    at getAdmins (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\adminController.js:113:26)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-26 02:45:16"}
{"level":"info","message":"::ffff:127.0.0.1 - - [26/Jul/2025:13:45:16 +0000] \"GET /api/admin HTTP/1.1\" 500 60 \"-\" \"Thunder Client (https://www.thunderclient.com)\"","service":"sms-backend","timestamp":"2025-07-26 02:45:16"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-26 02:45:25"}
{"level":"info","message":"Database connected successfully","service":"sms-backend","timestamp":"2025-07-26 02:45:25"}
{"level":"info","message":"Server running on port 5000 in undefined mode","service":"sms-backend","timestamp":"2025-07-26 02:45:25"}
{"error":"Incorrect arguments to mysqld_stmt_execute","level":"error","message":"Database query error:","params":[20,0],"query":"\n      SELECT \n        u.id,\n        u.uuid,\n        u.first_name,\n        u.last_name,\n        u.email,\n        u.phone,\n        u.status,\n        u.last_login,\n        u.created_at,\n        GROUP_CONCAT(r.name) as roles,\n        CONCAT(u.first_name, ' ', u.last_name) as full_name\n      FROM users u\n      LEFT JOIN user_roles ur ON u.id = ur.user_id\n      LEFT JOIN roles r ON ur.role_id = r.id\n      WHERE u.user_type = 'admin'\n      GROUP BY u.id\n      ORDER BY u.created_at DESC\n      LIMIT ? OFFSET ?\n    ","service":"sms-backend","timestamp":"2025-07-26 02:45:31"}
{"code":"ER_WRONG_ARGUMENTS","errno":1210,"level":"error","message":"Get admins error: Incorrect arguments to mysqld_stmt_execute","service":"sms-backend","sql":"\n      SELECT \n        u.id,\n        u.uuid,\n        u.first_name,\n        u.last_name,\n        u.email,\n        u.phone,\n        u.status,\n        u.last_login,\n        u.created_at,\n        GROUP_CONCAT(r.name) as roles,\n        CONCAT(u.first_name, ' ', u.last_name) as full_name\n      FROM users u\n      LEFT JOIN user_roles ur ON u.id = ur.user_id\n      LEFT JOIN roles r ON ur.role_id = r.id\n      WHERE u.user_type = 'admin'\n      GROUP BY u.id\n      ORDER BY u.created_at DESC\n      LIMIT ? OFFSET ?\n    ","sqlMessage":"Incorrect arguments to mysqld_stmt_execute","sqlState":"HY000","stack":"Error: Incorrect arguments to mysqld_stmt_execute\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\config\\database.js:48:31)\n    at getAdmins (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\adminController.js:113:26)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-26 02:45:31"}
{"level":"info","message":"::ffff:127.0.0.1 - - [26/Jul/2025:13:45:31 +0000] \"GET /api/admin HTTP/1.1\" 500 60 \"-\" \"Thunder Client (https://www.thunderclient.com)\"","service":"sms-backend","timestamp":"2025-07-26 02:45:31"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-26 02:46:07"}
{"level":"info","message":"Database connected successfully","service":"sms-backend","timestamp":"2025-07-26 02:46:07"}
{"level":"info","message":"Server running on port 5000 in undefined mode","service":"sms-backend","timestamp":"2025-07-26 02:46:07"}
{"level":"info","message":"Admin user created: <EMAIL> by user system","service":"sms-backend","timestamp":"2025-07-26 02:46:09"}
{"level":"info","message":"::ffff:127.0.0.1 - - [26/Jul/2025:13:46:09 +0000] \"POST /api/admin HTTP/1.1\" 201 211 \"-\" \"Thunder Client (https://www.thunderclient.com)\"","service":"sms-backend","timestamp":"2025-07-26 02:46:09"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-26 02:47:26"}
{"level":"info","message":"Database connected successfully","service":"sms-backend","timestamp":"2025-07-26 02:47:26"}
{"level":"info","message":"Server running on port 5000 in undefined mode","service":"sms-backend","timestamp":"2025-07-26 02:47:26"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-26 02:47:35"}
{"level":"info","message":"Database connected successfully","service":"sms-backend","timestamp":"2025-07-26 02:47:35"}
{"level":"info","message":"Server running on port 5000 in undefined mode","service":"sms-backend","timestamp":"2025-07-26 02:47:35"}
{"level":"error","message":"Error generating access token: secretOrPrivateKey must have a value","service":"sms-backend","stack":"Error: secretOrPrivateKey must have a value\n    at module.exports [as sign] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\jsonwebtoken\\sign.js:111:20)\n    at generateAccessToken (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\utils\\jwt.js:11:16)\n    at generateTokenPair (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\utils\\jwt.js:108:18)\n    at login (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\authController.js:57:20)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-26 02:48:53"}
{"level":"error","message":"Login error: Token generation failed","service":"sms-backend","stack":"Error: Token generation failed\n    at generateAccessToken (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\utils\\jwt.js:22:11)\n    at generateTokenPair (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\utils\\jwt.js:108:18)\n    at login (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\authController.js:57:20)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-26 02:48:53"}
{"level":"info","message":"::ffff:127.0.0.1 - - [26/Jul/2025:13:48:53 +0000] \"POST /api/auth/login HTTP/1.1\" 500 42 \"-\" \"Thunder Client (https://www.thunderclient.com)\"","service":"sms-backend","timestamp":"2025-07-26 02:48:53"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-26 02:49:43"}
{"level":"info","message":"Database connected successfully","service":"sms-backend","timestamp":"2025-07-26 02:49:43"}
{"level":"info","message":"Server running on port 5000 in undefined mode","service":"sms-backend","timestamp":"2025-07-26 02:49:43"}
{"level":"error","message":"Error generating access token: secretOrPrivateKey must have a value","service":"sms-backend","stack":"Error: secretOrPrivateKey must have a value\n    at module.exports [as sign] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\jsonwebtoken\\sign.js:111:20)\n    at generateAccessToken (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\utils\\jwt.js:11:16)\n    at generateTokenPair (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\utils\\jwt.js:108:18)\n    at login (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\authController.js:57:20)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-26 02:49:45"}
{"level":"error","message":"Login error: Token generation failed","service":"sms-backend","stack":"Error: Token generation failed\n    at generateAccessToken (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\utils\\jwt.js:22:11)\n    at generateTokenPair (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\utils\\jwt.js:108:18)\n    at login (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\authController.js:57:20)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-26 02:49:45"}
{"level":"info","message":"::ffff:127.0.0.1 - - [26/Jul/2025:13:49:45 +0000] \"POST /api/auth/login HTTP/1.1\" 500 42 \"-\" \"Thunder Client (https://www.thunderclient.com)\"","service":"sms-backend","timestamp":"2025-07-26 02:49:45"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-26 02:51:23"}
{"level":"info","message":"Database connected successfully","service":"sms-backend","timestamp":"2025-07-26 02:51:23"}
{"level":"info","message":"Server running on port 5000 in undefined mode","service":"sms-backend","timestamp":"2025-07-26 02:51:23"}
{"level":"error","message":"Error generating refresh token: secretOrPrivateKey must have a value","service":"sms-backend","stack":"Error: secretOrPrivateKey must have a value\n    at module.exports [as sign] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\jsonwebtoken\\sign.js:111:20)\n    at generateRefreshToken (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\utils\\jwt.js:33:16)\n    at generateTokenPair (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\utils\\jwt.js:109:19)\n    at login (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\authController.js:57:20)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-26 02:51:30"}
{"level":"error","message":"Login error: Refresh token generation failed","service":"sms-backend","stack":"Error: Refresh token generation failed\n    at generateRefreshToken (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\utils\\jwt.js:44:11)\n    at generateTokenPair (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\utils\\jwt.js:109:19)\n    at login (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\authController.js:57:20)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-26 02:51:30"}
{"level":"info","message":"::ffff:127.0.0.1 - - [26/Jul/2025:13:51:30 +0000] \"POST /api/auth/login HTTP/1.1\" 500 42 \"-\" \"Thunder Client (https://www.thunderclient.com)\"","service":"sms-backend","timestamp":"2025-07-26 02:51:30"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-26 02:52:02"}
{"level":"info","message":"Database connected successfully","service":"sms-backend","timestamp":"2025-07-26 02:52:02"}
{"level":"info","message":"Server running on port 5000 in undefined mode","service":"sms-backend","timestamp":"2025-07-26 02:52:02"}
{"level":"info","message":"User <EMAIL> logged in successfully","service":"sms-backend","timestamp":"2025-07-26 02:52:09"}
{"level":"info","message":"::ffff:127.0.0.1 - - [26/Jul/2025:13:52:09 +0000] \"POST /api/auth/login HTTP/1.1\" 200 846 \"-\" \"Thunder Client (https://www.thunderclient.com)\"","service":"sms-backend","timestamp":"2025-07-26 02:52:09"}
{"level":"info","message":"User <EMAIL> logged in successfully","service":"sms-backend","timestamp":"2025-07-26 02:56:54"}
{"level":"info","message":"::1 - - [26/Jul/2025:13:56:54 +0000] \"POST /api/auth/login HTTP/1.1\" 200 846 \"http://localhost:3000/\" \"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-26 02:56:54"}
{"level":"error","message":"Authentication error: Invalid token","service":"sms-backend","stack":"Error: Invalid token\n    at verifyAccessToken (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\utils\\jwt.js:63:13)\n    at authenticate (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\middleware\\auth.js:22:21)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:47:12)","timestamp":"2025-07-26 02:57:01"}
{"level":"info","message":"::1 - - [26/Jul/2025:13:57:01 +0000] \"GET /api/analytics/dashboard HTTP/1.1\" 401 66 \"http://localhost:3000/\" \"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-26 02:57:01"}
{"level":"error","message":"Authentication error: Invalid token","service":"sms-backend","stack":"Error: Invalid token\n    at verifyAccessToken (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\utils\\jwt.js:63:13)\n    at authenticate (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\middleware\\auth.js:22:21)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:47:12)","timestamp":"2025-07-26 02:57:01"}
{"level":"info","message":"::1 - - [26/Jul/2025:13:57:01 +0000] \"GET /api/analytics/dashboard HTTP/1.1\" 401 66 \"http://localhost:3000/\" \"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-26 02:57:01"}
{"level":"info","message":"User <EMAIL> logged in successfully","service":"sms-backend","timestamp":"2025-07-26 02:57:11"}
{"level":"info","message":"::1 - - [26/Jul/2025:13:57:11 +0000] \"POST /api/auth/login HTTP/1.1\" 200 846 \"http://localhost:3000/\" \"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-26 02:57:11"}
{"level":"error","message":"Authentication error: Invalid token","service":"sms-backend","stack":"Error: Invalid token\n    at verifyAccessToken (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\utils\\jwt.js:63:13)\n    at authenticate (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\middleware\\auth.js:22:21)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:47:12)","timestamp":"2025-07-26 02:57:12"}
{"level":"info","message":"::1 - - [26/Jul/2025:13:57:12 +0000] \"GET /api/analytics/dashboard HTTP/1.1\" 401 66 \"http://localhost:3000/\" \"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-26 02:57:12"}
{"level":"error","message":"Authentication error: Invalid token","service":"sms-backend","stack":"Error: Invalid token\n    at verifyAccessToken (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\utils\\jwt.js:63:13)\n    at authenticate (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\middleware\\auth.js:22:21)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:47:12)","timestamp":"2025-07-26 02:57:12"}
{"level":"info","message":"::1 - - [26/Jul/2025:13:57:12 +0000] \"GET /api/analytics/dashboard HTTP/1.1\" 401 66 \"http://localhost:3000/\" \"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-26 02:57:12"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-26 03:00:29"}
{"level":"info","message":"Database connected successfully","service":"sms-backend","timestamp":"2025-07-26 03:00:29"}
{"level":"info","message":"Server running on port 5000 in undefined mode","service":"sms-backend","timestamp":"2025-07-26 03:00:29"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-26 03:00:43"}
{"level":"info","message":"Database connected successfully","service":"sms-backend","timestamp":"2025-07-26 03:00:43"}
{"level":"info","message":"Server running on port 5000 in undefined mode","service":"sms-backend","timestamp":"2025-07-26 03:00:43"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-26 03:02:10"}
{"level":"info","message":"User <EMAIL> logged in successfully","service":"sms-backend","timestamp":"2025-07-26 03:03:10"}
{"level":"info","message":"::1 - - [26/Jul/2025:14:03:10 +0000] \"POST /api/auth/login HTTP/1.1\" 200 846 \"http://localhost:3000/\" \"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-26 03:03:10"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-26 03:03:17"}
{"level":"info","message":"Database connected successfully","service":"sms-backend","timestamp":"2025-07-26 03:03:17"}
{"level":"info","message":"Server running on port 5000 in undefined mode","service":"sms-backend","timestamp":"2025-07-26 03:03:17"}
{"error":"Cannot read properties of undefined (reading 'execute')","level":"error","message":"Database query error:","params":["<EMAIL>"],"query":"SELECT id FROM users WHERE email = ?","service":"sms-backend","timestamp":"2025-07-26 03:03:26"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-26 03:03:47"}
{"level":"info","message":"Database connected successfully","service":"sms-backend","timestamp":"2025-07-26 03:03:47"}
{"level":"info","message":"Server running on port 5000 in undefined mode","service":"sms-backend","timestamp":"2025-07-26 03:03:47"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-26 03:03:54"}
{"level":"info","message":"Database connected successfully","service":"sms-backend","timestamp":"2025-07-26 03:03:54"}
{"level":"info","message":"Server running on port 5000 in undefined mode","service":"sms-backend","timestamp":"2025-07-26 03:03:54"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-26 03:04:05"}
{"level":"error","message":"Authentication error: Invalid token","service":"sms-backend","stack":"Error: Invalid token\n    at verifyAccessToken (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\utils\\jwt.js:63:13)\n    at authenticate (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\middleware\\auth.js:22:21)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:346:12)","timestamp":"2025-07-26 03:19:20"}
{"level":"info","message":"::1 - - [26/Jul/2025:14:19:20 +0000] \"GET /api/auth/profile HTTP/1.1\" 401 66 \"http://localhost:3000/\" \"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-26 03:19:20"}
{"level":"error","message":"Authentication error: Invalid token","service":"sms-backend","stack":"Error: Invalid token\n    at verifyAccessToken (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\utils\\jwt.js:63:13)\n    at authenticate (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\middleware\\auth.js:22:21)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:346:12)","timestamp":"2025-07-26 03:20:00"}
{"level":"info","message":"::1 - - [26/Jul/2025:14:20:00 +0000] \"GET /api/auth/profile HTTP/1.1\" 401 66 \"http://localhost:3000/\" \"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-26 03:20:00"}
{"level":"error","message":"Authentication error: Invalid token","service":"sms-backend","stack":"Error: Invalid token\n    at verifyAccessToken (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\utils\\jwt.js:63:13)\n    at authenticate (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\middleware\\auth.js:22:21)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:346:12)","timestamp":"2025-07-26 03:21:11"}
{"level":"info","message":"::1 - - [26/Jul/2025:14:21:11 +0000] \"GET /api/auth/profile HTTP/1.1\" 401 66 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-26 03:21:11"}
{"level":"error","message":"Authentication error: Invalid token","service":"sms-backend","stack":"Error: Invalid token\n    at verifyAccessToken (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\utils\\jwt.js:63:13)\n    at authenticate (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\middleware\\auth.js:22:21)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:47:12)","timestamp":"2025-07-26 03:21:11"}
{"level":"info","message":"::1 - - [26/Jul/2025:14:21:11 +0000] \"GET /api/analytics/dashboard HTTP/1.1\" 401 66 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-26 03:21:11"}
{"level":"error","message":"Authentication error: Invalid token","service":"sms-backend","stack":"Error: Invalid token\n    at verifyAccessToken (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\utils\\jwt.js:63:13)\n    at authenticate (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\middleware\\auth.js:22:21)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:47:12)","timestamp":"2025-07-26 03:21:11"}
{"level":"info","message":"::1 - - [26/Jul/2025:14:21:11 +0000] \"GET /api/analytics/dashboard HTTP/1.1\" 401 66 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-26 03:21:11"}
{"level":"info","message":"User <EMAIL> logged in successfully","service":"sms-backend","timestamp":"2025-07-26 03:21:43"}
{"level":"info","message":"::1 - - [26/Jul/2025:14:21:43 +0000] \"POST /api/auth/login HTTP/1.1\" 200 846 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-26 03:21:43"}
{"level":"error","message":"Authentication error: Invalid token","service":"sms-backend","stack":"Error: Invalid token\n    at verifyAccessToken (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\utils\\jwt.js:63:13)\n    at authenticate (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\middleware\\auth.js:22:21)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:47:12)","timestamp":"2025-07-26 03:21:44"}
{"level":"info","message":"::1 - - [26/Jul/2025:14:21:44 +0000] \"GET /api/analytics/dashboard HTTP/1.1\" 401 66 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-26 03:21:44"}
{"level":"error","message":"Authentication error: Invalid token","service":"sms-backend","stack":"Error: Invalid token\n    at verifyAccessToken (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\utils\\jwt.js:63:13)\n    at authenticate (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\middleware\\auth.js:22:21)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:47:12)","timestamp":"2025-07-26 03:21:44"}
{"level":"info","message":"::1 - - [26/Jul/2025:14:21:44 +0000] \"GET /api/analytics/dashboard HTTP/1.1\" 401 66 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-26 03:21:44"}
{"level":"info","message":"User <EMAIL> logged in successfully","service":"sms-backend","timestamp":"2025-07-26 03:21:47"}
{"level":"info","message":"::1 - - [26/Jul/2025:14:21:47 +0000] \"POST /api/auth/login HTTP/1.1\" 200 846 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-26 03:21:47"}
{"level":"error","message":"Authentication error: Invalid token","service":"sms-backend","stack":"Error: Invalid token\n    at verifyAccessToken (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\utils\\jwt.js:63:13)\n    at authenticate (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\middleware\\auth.js:22:21)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:47:12)","timestamp":"2025-07-26 03:21:48"}
{"level":"info","message":"::1 - - [26/Jul/2025:14:21:48 +0000] \"GET /api/analytics/dashboard HTTP/1.1\" 401 66 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-26 03:21:48"}
{"level":"error","message":"Authentication error: Invalid token","service":"sms-backend","stack":"Error: Invalid token\n    at verifyAccessToken (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\utils\\jwt.js:63:13)\n    at authenticate (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\middleware\\auth.js:22:21)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:47:12)","timestamp":"2025-07-26 03:21:48"}
{"level":"info","message":"::1 - - [26/Jul/2025:14:21:48 +0000] \"GET /api/analytics/dashboard HTTP/1.1\" 401 66 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-26 03:21:48"}
{"level":"info","message":"User <EMAIL> logged in successfully","service":"sms-backend","timestamp":"2025-07-26 03:23:28"}
{"level":"info","message":"::1 - - [26/Jul/2025:14:23:28 +0000] \"POST /api/auth/login HTTP/1.1\" 200 846 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-26 03:23:28"}
{"level":"error","message":"Authentication error: Invalid token","service":"sms-backend","stack":"Error: Invalid token\n    at verifyAccessToken (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\utils\\jwt.js:63:13)\n    at authenticate (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\middleware\\auth.js:22:21)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:47:12)","timestamp":"2025-07-26 03:23:30"}
{"level":"info","message":"::1 - - [26/Jul/2025:14:23:30 +0000] \"GET /api/analytics/dashboard HTTP/1.1\" 401 66 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-26 03:23:30"}
{"level":"error","message":"Authentication error: Invalid token","service":"sms-backend","stack":"Error: Invalid token\n    at verifyAccessToken (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\utils\\jwt.js:63:13)\n    at authenticate (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\middleware\\auth.js:22:21)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:47:12)","timestamp":"2025-07-26 03:23:30"}
{"level":"info","message":"::1 - - [26/Jul/2025:14:23:30 +0000] \"GET /api/analytics/dashboard HTTP/1.1\" 401 66 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-26 03:23:30"}
{"level":"info","message":"::1 - - [26/Jul/2025:14:30:38 +0000] \"POST /api/auth/login HTTP/1.1\" 401 55 \"-\" \"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.19041.6093\"","service":"sms-backend","timestamp":"2025-07-26 03:30:38"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-26 03:30:48"}
{"level":"info","message":"::1 - - [26/Jul/2025:14:31:00 +0000] \"POST /api/auth/login HTTP/1.1\" 401 55 \"-\" \"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.19041.6093\"","service":"sms-backend","timestamp":"2025-07-26 03:31:00"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-26 03:31:24"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-26 03:32:24"}
{"level":"info","message":"Database connected successfully","service":"sms-backend","timestamp":"2025-07-26 03:32:24"}
{"level":"info","message":"Server running on port 5000 in undefined mode","service":"sms-backend","timestamp":"2025-07-26 03:32:24"}
{"level":"info","message":"::1 - - [26/Jul/2025:14:32:53 +0000] \"POST /api/auth/login HTTP/1.1\" 401 55 \"-\" \"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.19041.6093\"","service":"sms-backend","timestamp":"2025-07-26 03:32:53"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-26 03:33:10"}
{"level":"info","message":"Database connected successfully","service":"sms-backend","timestamp":"2025-07-26 03:33:10"}
{"level":"info","message":"Server running on port 5000 in undefined mode","service":"sms-backend","timestamp":"2025-07-26 03:33:10"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-26 03:33:11"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-26 03:33:20"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-26 03:33:42"}
{"level":"info","message":"Database connected successfully","service":"sms-backend","timestamp":"2025-07-26 03:33:42"}
{"level":"info","message":"Server running on port 5000 in undefined mode","service":"sms-backend","timestamp":"2025-07-26 03:33:42"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-26 03:33:42"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-26 03:33:48"}
{"level":"info","message":"User <EMAIL> logged in successfully","service":"sms-backend","timestamp":"2025-07-26 03:33:59"}
{"level":"info","message":"::1 - - [26/Jul/2025:14:33:59 +0000] \"POST /api/auth/login HTTP/1.1\" 200 846 \"-\" \"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.19041.6093\"","service":"sms-backend","timestamp":"2025-07-26 03:33:59"}
{"level":"info","message":"::1 - - [26/Jul/2025:14:38:32 +0000] \"POST /api/auth/login HTTP/1.1\" 401 55 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-26 03:38:32"}
{"level":"info","message":"::1 - - [26/Jul/2025:14:38:46 +0000] \"POST /api/auth/login HTTP/1.1\" 401 55 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-26 03:38:46"}
{"level":"info","message":"::1 - - [26/Jul/2025:14:39:02 +0000] \"POST /api/auth/login HTTP/1.1\" 401 55 \"http://localhost:3000/\" \"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-26 03:39:02"}
{"level":"info","message":"::1 - - [26/Jul/2025:14:39:31 +0000] \"POST /api/auth/login HTTP/1.1\" 401 55 \"http://localhost:3000/\" \"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-26 03:39:31"}
{"level":"info","message":"::ffff:127.0.0.1 - - [26/Jul/2025:14:40:03 +0000] \"POST /api/auth/login HTTP/1.1\" 401 55 \"-\" \"Thunder Client (https://www.thunderclient.com)\"","service":"sms-backend","timestamp":"2025-07-26 03:40:03"}
{"level":"info","message":"SIGINT received. Shutting down gracefully...","service":"sms-backend","timestamp":"2025-07-26 03:40:17"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-26 03:40:34"}
{"level":"info","message":"Database connected successfully","service":"sms-backend","timestamp":"2025-07-26 03:40:34"}
{"level":"info","message":"Server running on port 5000 in undefined mode","service":"sms-backend","timestamp":"2025-07-26 03:40:34"}
{"level":"info","message":"::ffff:127.0.0.1 - - [26/Jul/2025:14:40:47 +0000] \"POST /api/auth/login HTTP/1.1\" 401 55 \"-\" \"Thunder Client (https://www.thunderclient.com)\"","service":"sms-backend","timestamp":"2025-07-26 03:40:47"}
{"level":"info","message":"::ffff:127.0.0.1 - - [26/Jul/2025:14:41:00 +0000] \"POST /api/admin HTTP/1.1\" 401 51 \"-\" \"Thunder Client (https://www.thunderclient.com)\"","service":"sms-backend","timestamp":"2025-07-26 03:41:00"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-26 03:41:25"}
{"level":"info","message":"Database connected successfully","service":"sms-backend","timestamp":"2025-07-26 03:41:25"}
{"level":"info","message":"Server running on port 5000 in undefined mode","service":"sms-backend","timestamp":"2025-07-26 03:41:25"}
{"level":"info","message":"::ffff:127.0.0.1 - - [26/Jul/2025:14:41:28 +0000] \"POST /api/admin HTTP/1.1\" 400 50 \"-\" \"Thunder Client (https://www.thunderclient.com)\"","service":"sms-backend","timestamp":"2025-07-26 03:41:28"}
{"level":"info","message":"Admin user created: <EMAIL> by user system","service":"sms-backend","timestamp":"2025-07-26 03:41:42"}
{"level":"info","message":"::ffff:127.0.0.1 - - [26/Jul/2025:14:41:42 +0000] \"POST /api/admin HTTP/1.1\" 201 212 \"-\" \"Thunder Client (https://www.thunderclient.com)\"","service":"sms-backend","timestamp":"2025-07-26 03:41:42"}
{"level":"info","message":"User <EMAIL> logged in successfully","service":"sms-backend","timestamp":"2025-07-26 03:41:53"}
{"level":"info","message":"::1 - - [26/Jul/2025:14:41:53 +0000] \"POST /api/auth/login HTTP/1.1\" 200 849 \"http://localhost:3000/\" \"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-26 03:41:53"}
{"level":"error","message":"Authentication error: Invalid token","service":"sms-backend","stack":"Error: Invalid token\n    at verifyAccessToken (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\utils\\jwt.js:63:13)\n    at authenticate (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\middleware\\auth.js:22:21)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:47:12)","timestamp":"2025-07-26 03:41:54"}
{"level":"info","message":"::1 - - [26/Jul/2025:14:41:54 +0000] \"GET /api/analytics/dashboard HTTP/1.1\" 401 66 \"http://localhost:3000/\" \"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-26 03:41:54"}
{"level":"error","message":"Authentication error: Invalid token","service":"sms-backend","stack":"Error: Invalid token\n    at verifyAccessToken (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\utils\\jwt.js:63:13)\n    at authenticate (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\middleware\\auth.js:22:21)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:47:12)","timestamp":"2025-07-26 03:41:55"}
{"level":"info","message":"::1 - - [26/Jul/2025:14:41:55 +0000] \"GET /api/analytics/dashboard HTTP/1.1\" 401 66 \"http://localhost:3000/\" \"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-26 03:41:55"}
{"level":"error","message":"Authentication error: Invalid token","service":"sms-backend","stack":"Error: Invalid token\n    at verifyAccessToken (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\utils\\jwt.js:63:13)\n    at authenticate (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\middleware\\auth.js:22:21)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:346:12)","timestamp":"2025-07-26 03:42:15"}
{"level":"info","message":"::1 - - [26/Jul/2025:14:42:15 +0000] \"GET /api/auth/profile HTTP/1.1\" 401 66 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-26 03:42:15"}
{"level":"error","message":"Authentication error: Invalid token","service":"sms-backend","stack":"Error: Invalid token\n    at verifyAccessToken (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\utils\\jwt.js:63:13)\n    at authenticate (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\middleware\\auth.js:22:21)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:47:12)","timestamp":"2025-07-26 03:42:15"}
{"level":"info","message":"::1 - - [26/Jul/2025:14:42:15 +0000] \"GET /api/analytics/dashboard HTTP/1.1\" 401 66 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-26 03:42:15"}
{"level":"error","message":"Authentication error: Invalid token","service":"sms-backend","stack":"Error: Invalid token\n    at verifyAccessToken (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\utils\\jwt.js:63:13)\n    at authenticate (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\middleware\\auth.js:22:21)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:47:12)","timestamp":"2025-07-26 03:42:15"}
{"level":"info","message":"::1 - - [26/Jul/2025:14:42:15 +0000] \"GET /api/analytics/dashboard HTTP/1.1\" 401 66 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-26 03:42:15"}
{"level":"error","message":"Authentication error: Invalid token","service":"sms-backend","stack":"Error: Invalid token\n    at verifyAccessToken (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\utils\\jwt.js:63:13)\n    at authenticate (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\middleware\\auth.js:22:21)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:346:12)","timestamp":"2025-07-26 03:42:35"}
{"level":"info","message":"::1 - - [26/Jul/2025:14:42:35 +0000] \"GET /api/auth/profile HTTP/1.1\" 401 66 \"http://localhost:3000/\" \"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-26 03:42:35"}
{"level":"error","message":"Authentication error: Invalid token","service":"sms-backend","stack":"Error: Invalid token\n    at verifyAccessToken (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\utils\\jwt.js:63:13)\n    at authenticate (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\middleware\\auth.js:22:21)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:47:12)","timestamp":"2025-07-26 03:42:35"}
{"level":"info","message":"::1 - - [26/Jul/2025:14:42:35 +0000] \"GET /api/analytics/dashboard HTTP/1.1\" 401 66 \"http://localhost:3000/\" \"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-26 03:42:35"}
{"level":"error","message":"Authentication error: Invalid token","service":"sms-backend","stack":"Error: Invalid token\n    at verifyAccessToken (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\utils\\jwt.js:63:13)\n    at authenticate (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\middleware\\auth.js:22:21)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:47:12)","timestamp":"2025-07-26 03:42:35"}
{"level":"info","message":"::1 - - [26/Jul/2025:14:42:35 +0000] \"GET /api/analytics/dashboard HTTP/1.1\" 401 66 \"http://localhost:3000/\" \"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-26 03:42:35"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-26 03:45:01"}
{"level":"info","message":"Database connected successfully","service":"sms-backend","timestamp":"2025-07-26 03:45:01"}
{"level":"info","message":"Server running on port 5000 in undefined mode","service":"sms-backend","timestamp":"2025-07-26 03:45:01"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-26 03:45:23"}
{"level":"info","message":"Database connected successfully","service":"sms-backend","timestamp":"2025-07-26 03:45:23"}
{"level":"info","message":"Server running on port 5000 in undefined mode","service":"sms-backend","timestamp":"2025-07-26 03:45:23"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-26 03:46:13"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-26 03:47:07"}
{"level":"info","message":"Database connected successfully","service":"sms-backend","timestamp":"2025-07-26 03:47:07"}
{"level":"info","message":"Server running on port 5000 in development mode","service":"sms-backend","timestamp":"2025-07-26 03:47:07"}
{"level":"info","message":"User <EMAIL> logged in successfully","service":"sms-backend","timestamp":"2025-07-26 03:47:33"}
{"level":"info","message":"::1 - - [26/Jul/2025:14:47:33 +0000] \"POST /api/auth/login HTTP/1.1\" 200 846 \"-\" \"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.19041.6093\"","service":"sms-backend","timestamp":"2025-07-26 03:47:33"}
{"level":"info","message":"User <EMAIL> logged in successfully","service":"sms-backend","timestamp":"2025-07-26 03:47:42"}
{"level":"info","message":"::1 - - [26/Jul/2025:14:47:42 +0000] \"POST /api/auth/login HTTP/1.1\" 200 846 \"-\" \"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.19041.6093\"","service":"sms-backend","timestamp":"2025-07-26 03:47:42"}
{"error":"Unknown column 'date' in 'where clause'","level":"error","message":"Database query error:","params":["2025-07-26"],"query":"\n        SELECT\n          COUNT(*) as total_records,\n          SUM(CASE WHEN status = 'present' THEN 1 ELSE 0 END) as present_count,\n          SUM(CASE WHEN status = 'absent' THEN 1 ELSE 0 END) as absent_count,\n          SUM(CASE WHEN status = 'late' THEN 1 ELSE 0 END) as late_count\n        FROM attendance\n        WHERE date = ?\n      ","service":"sms-backend","timestamp":"2025-07-26 03:47:42"}
{"error":"Unknown column 'type' in 'field list'","level":"error","message":"Database query error:","params":[],"query":"\n        SELECT title, start_date, start_time, type, location\n        FROM events\n        WHERE start_date >= CURDATE() AND status = 'active'\n        ORDER BY start_date ASC, start_time ASC\n        LIMIT 5\n      ","service":"sms-backend","timestamp":"2025-07-26 03:47:42"}
{"level":"info","message":"::1 - - [26/Jul/2025:14:47:42 +0000] \"GET /api/analytics/dashboard HTTP/1.1\" 200 367 \"-\" \"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.19041.6093\"","service":"sms-backend","timestamp":"2025-07-26 03:47:42"}
{"level":"info","message":"User <EMAIL> logged in successfully","service":"sms-backend","timestamp":"2025-07-26 03:50:20"}
{"level":"info","message":"::1 - - [26/Jul/2025:14:50:20 +0000] \"POST /api/auth/login HTTP/1.1\" 200 849 \"http://localhost:3001/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-26 03:50:20"}
{"error":"Unknown column 'date' in 'where clause'","level":"error","message":"Database query error:","params":["2025-07-26"],"query":"\n        SELECT\n          COUNT(*) as total_records,\n          SUM(CASE WHEN status = 'present' THEN 1 ELSE 0 END) as present_count,\n          SUM(CASE WHEN status = 'absent' THEN 1 ELSE 0 END) as absent_count,\n          SUM(CASE WHEN status = 'late' THEN 1 ELSE 0 END) as late_count\n        FROM attendance\n        WHERE date = ?\n      ","service":"sms-backend","timestamp":"2025-07-26 03:50:27"}
{"error":"Unknown column 'type' in 'field list'","level":"error","message":"Database query error:","params":[],"query":"\n        SELECT title, start_date, start_time, type, location\n        FROM events\n        WHERE start_date >= CURDATE() AND status = 'active'\n        ORDER BY start_date ASC, start_time ASC\n        LIMIT 5\n      ","service":"sms-backend","timestamp":"2025-07-26 03:50:27"}
{"level":"info","message":"::1 - - [26/Jul/2025:14:50:27 +0000] \"GET /api/analytics/dashboard HTTP/1.1\" 200 367 \"http://localhost:3001/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-26 03:50:27"}
{"error":"Unknown column 'date' in 'where clause'","level":"error","message":"Database query error:","params":["2025-07-26"],"query":"\n        SELECT\n          COUNT(*) as total_records,\n          SUM(CASE WHEN status = 'present' THEN 1 ELSE 0 END) as present_count,\n          SUM(CASE WHEN status = 'absent' THEN 1 ELSE 0 END) as absent_count,\n          SUM(CASE WHEN status = 'late' THEN 1 ELSE 0 END) as late_count\n        FROM attendance\n        WHERE date = ?\n      ","service":"sms-backend","timestamp":"2025-07-26 03:50:27"}
{"error":"Unknown column 'type' in 'field list'","level":"error","message":"Database query error:","params":[],"query":"\n        SELECT title, start_date, start_time, type, location\n        FROM events\n        WHERE start_date >= CURDATE() AND status = 'active'\n        ORDER BY start_date ASC, start_time ASC\n        LIMIT 5\n      ","service":"sms-backend","timestamp":"2025-07-26 03:50:27"}
{"level":"info","message":"::1 - - [26/Jul/2025:14:50:27 +0000] \"GET /api/analytics/dashboard HTTP/1.1\" 304 - \"http://localhost:3001/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-26 03:50:27"}
{"error":"Table 'school_management_system.admins' doesn't exist","level":"error","message":"Database query error:","params":[2],"query":"SELECT * FROM admins WHERE user_id = ?","service":"sms-backend","timestamp":"2025-07-26 03:50:56"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Get profile error: Table 'school_management_system.admins' doesn't exist","service":"sms-backend","sql":"SELECT * FROM admins WHERE user_id = ?","sqlMessage":"Table 'school_management_system.admins' doesn't exist","sqlState":"42S02","stack":"Error: Table 'school_management_system.admins' doesn't exist\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\config\\database.js:48:31)\n    at getProfile (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\authController.js:256:30)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-26 03:50:56"}
{"level":"info","message":"::1 - - [26/Jul/2025:14:50:56 +0000] \"GET /api/auth/profile HTTP/1.1\" 500 51 \"http://localhost:3001/\" \"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-26 03:50:56"}
{"error":"Unknown column 'date' in 'where clause'","level":"error","message":"Database query error:","params":["2025-07-26"],"query":"\n        SELECT\n          COUNT(*) as total_records,\n          SUM(CASE WHEN status = 'present' THEN 1 ELSE 0 END) as present_count,\n          SUM(CASE WHEN status = 'absent' THEN 1 ELSE 0 END) as absent_count,\n          SUM(CASE WHEN status = 'late' THEN 1 ELSE 0 END) as late_count\n        FROM attendance\n        WHERE date = ?\n      ","service":"sms-backend","timestamp":"2025-07-26 03:50:57"}
{"error":"Unknown column 'type' in 'field list'","level":"error","message":"Database query error:","params":[],"query":"\n        SELECT title, start_date, start_time, type, location\n        FROM events\n        WHERE start_date >= CURDATE() AND status = 'active'\n        ORDER BY start_date ASC, start_time ASC\n        LIMIT 5\n      ","service":"sms-backend","timestamp":"2025-07-26 03:50:57"}
{"level":"info","message":"::1 - - [26/Jul/2025:14:50:57 +0000] \"GET /api/analytics/dashboard HTTP/1.1\" 304 - \"http://localhost:3001/\" \"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-26 03:50:57"}
{"error":"Unknown column 'date' in 'where clause'","level":"error","message":"Database query error:","params":["2025-07-26"],"query":"\n        SELECT\n          COUNT(*) as total_records,\n          SUM(CASE WHEN status = 'present' THEN 1 ELSE 0 END) as present_count,\n          SUM(CASE WHEN status = 'absent' THEN 1 ELSE 0 END) as absent_count,\n          SUM(CASE WHEN status = 'late' THEN 1 ELSE 0 END) as late_count\n        FROM attendance\n        WHERE date = ?\n      ","service":"sms-backend","timestamp":"2025-07-26 03:50:57"}
{"error":"Unknown column 'type' in 'field list'","level":"error","message":"Database query error:","params":[],"query":"\n        SELECT title, start_date, start_time, type, location\n        FROM events\n        WHERE start_date >= CURDATE() AND status = 'active'\n        ORDER BY start_date ASC, start_time ASC\n        LIMIT 5\n      ","service":"sms-backend","timestamp":"2025-07-26 03:50:57"}
{"level":"info","message":"::1 - - [26/Jul/2025:14:50:57 +0000] \"GET /api/analytics/dashboard HTTP/1.1\" 304 - \"http://localhost:3001/\" \"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-26 03:50:57"}
{"error":"Table 'school_management_system.admins' doesn't exist","level":"error","message":"Database query error:","params":[2],"query":"SELECT * FROM admins WHERE user_id = ?","service":"sms-backend","timestamp":"2025-07-26 03:51:09"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Get profile error: Table 'school_management_system.admins' doesn't exist","service":"sms-backend","sql":"SELECT * FROM admins WHERE user_id = ?","sqlMessage":"Table 'school_management_system.admins' doesn't exist","sqlState":"42S02","stack":"Error: Table 'school_management_system.admins' doesn't exist\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\config\\database.js:48:31)\n    at getProfile (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\authController.js:256:30)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-26 03:51:09"}
{"level":"info","message":"::1 - - [26/Jul/2025:14:51:09 +0000] \"GET /api/auth/profile HTTP/1.1\" 500 51 \"http://localhost:3001/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-26 03:51:09"}
{"error":"Table 'school_management_system.admins' doesn't exist","level":"error","message":"Database query error:","params":[2],"query":"SELECT * FROM admins WHERE user_id = ?","service":"sms-backend","timestamp":"2025-07-26 03:51:15"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Get profile error: Table 'school_management_system.admins' doesn't exist","service":"sms-backend","sql":"SELECT * FROM admins WHERE user_id = ?","sqlMessage":"Table 'school_management_system.admins' doesn't exist","sqlState":"42S02","stack":"Error: Table 'school_management_system.admins' doesn't exist\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\config\\database.js:48:31)\n    at getProfile (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\authController.js:256:30)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-26 03:51:15"}
{"level":"info","message":"::1 - - [26/Jul/2025:14:51:15 +0000] \"GET /api/auth/profile HTTP/1.1\" 500 51 \"http://localhost:3001/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-26 03:51:15"}
{"error":"Unknown column 'date' in 'where clause'","level":"error","message":"Database query error:","params":["2025-07-26"],"query":"\n        SELECT\n          COUNT(*) as total_records,\n          SUM(CASE WHEN status = 'present' THEN 1 ELSE 0 END) as present_count,\n          SUM(CASE WHEN status = 'absent' THEN 1 ELSE 0 END) as absent_count,\n          SUM(CASE WHEN status = 'late' THEN 1 ELSE 0 END) as late_count\n        FROM attendance\n        WHERE date = ?\n      ","service":"sms-backend","timestamp":"2025-07-26 03:51:15"}
{"error":"Unknown column 'type' in 'field list'","level":"error","message":"Database query error:","params":[],"query":"\n        SELECT title, start_date, start_time, type, location\n        FROM events\n        WHERE start_date >= CURDATE() AND status = 'active'\n        ORDER BY start_date ASC, start_time ASC\n        LIMIT 5\n      ","service":"sms-backend","timestamp":"2025-07-26 03:51:15"}
{"level":"info","message":"::1 - - [26/Jul/2025:14:51:15 +0000] \"GET /api/analytics/dashboard HTTP/1.1\" 304 - \"http://localhost:3001/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-26 03:51:15"}
{"error":"Unknown column 'date' in 'where clause'","level":"error","message":"Database query error:","params":["2025-07-26"],"query":"\n        SELECT\n          COUNT(*) as total_records,\n          SUM(CASE WHEN status = 'present' THEN 1 ELSE 0 END) as present_count,\n          SUM(CASE WHEN status = 'absent' THEN 1 ELSE 0 END) as absent_count,\n          SUM(CASE WHEN status = 'late' THEN 1 ELSE 0 END) as late_count\n        FROM attendance\n        WHERE date = ?\n      ","service":"sms-backend","timestamp":"2025-07-26 03:51:15"}
{"error":"Unknown column 'type' in 'field list'","level":"error","message":"Database query error:","params":[],"query":"\n        SELECT title, start_date, start_time, type, location\n        FROM events\n        WHERE start_date >= CURDATE() AND status = 'active'\n        ORDER BY start_date ASC, start_time ASC\n        LIMIT 5\n      ","service":"sms-backend","timestamp":"2025-07-26 03:51:15"}
{"level":"info","message":"::1 - - [26/Jul/2025:14:51:15 +0000] \"GET /api/analytics/dashboard HTTP/1.1\" 304 - \"http://localhost:3001/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-26 03:51:15"}
{"error":"Table 'school_management_system.admins' doesn't exist","level":"error","message":"Database query error:","params":[2],"query":"SELECT * FROM admins WHERE user_id = ?","service":"sms-backend","timestamp":"2025-07-26 03:51:31"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Get profile error: Table 'school_management_system.admins' doesn't exist","service":"sms-backend","sql":"SELECT * FROM admins WHERE user_id = ?","sqlMessage":"Table 'school_management_system.admins' doesn't exist","sqlState":"42S02","stack":"Error: Table 'school_management_system.admins' doesn't exist\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\config\\database.js:48:31)\n    at getProfile (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\authController.js:256:30)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-26 03:51:31"}
{"level":"info","message":"::1 - - [26/Jul/2025:14:51:31 +0000] \"GET /api/auth/profile HTTP/1.1\" 500 51 \"http://localhost:3001/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-26 03:51:31"}
{"level":"info","message":"::1 - - [26/Jul/2025:14:51:31 +0000] \"GET /api/students?page=1&limit=10&search=&sort_by=first_name&sort_order=asc HTTP/1.1\" 400 138 \"http://localhost:3001/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-26 03:51:31"}
{"level":"info","message":"::1 - - [26/Jul/2025:14:51:31 +0000] \"GET /api/students?page=1&limit=10&search=&sort_by=first_name&sort_order=asc HTTP/1.1\" 400 138 \"http://localhost:3001/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-26 03:51:31"}
{"error":"Table 'school_management_system.admins' doesn't exist","level":"error","message":"Database query error:","params":[2],"query":"SELECT * FROM admins WHERE user_id = ?","service":"sms-backend","timestamp":"2025-07-26 03:52:16"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Get profile error: Table 'school_management_system.admins' doesn't exist","service":"sms-backend","sql":"SELECT * FROM admins WHERE user_id = ?","sqlMessage":"Table 'school_management_system.admins' doesn't exist","sqlState":"42S02","stack":"Error: Table 'school_management_system.admins' doesn't exist\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\config\\database.js:48:31)\n    at getProfile (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\authController.js:256:30)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-26 03:52:16"}
{"level":"info","message":"::1 - - [26/Jul/2025:14:52:16 +0000] \"GET /api/auth/profile HTTP/1.1\" 500 51 \"http://localhost:3001/\" \"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-26 03:52:16"}
{"level":"info","message":"::1 - - [26/Jul/2025:14:52:16 +0000] \"GET /api/students?page=1&limit=10&search=&sort_by=first_name&sort_order=asc HTTP/1.1\" 400 138 \"http://localhost:3001/\" \"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-26 03:52:16"}
{"level":"info","message":"::1 - - [26/Jul/2025:14:52:16 +0000] \"GET /api/students?page=1&limit=10&search=&sort_by=first_name&sort_order=asc HTTP/1.1\" 400 138 \"http://localhost:3001/\" \"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-26 03:52:16"}
{"error":"Table 'school_management_system.admins' doesn't exist","level":"error","message":"Database query error:","params":[2],"query":"SELECT * FROM admins WHERE user_id = ?","service":"sms-backend","timestamp":"2025-07-26 03:54:14"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Get profile error: Table 'school_management_system.admins' doesn't exist","service":"sms-backend","sql":"SELECT * FROM admins WHERE user_id = ?","sqlMessage":"Table 'school_management_system.admins' doesn't exist","sqlState":"42S02","stack":"Error: Table 'school_management_system.admins' doesn't exist\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\config\\database.js:48:31)\n    at getProfile (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\authController.js:256:30)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-26 03:54:14"}
{"level":"info","message":"::1 - - [26/Jul/2025:14:54:14 +0000] \"GET /api/auth/profile HTTP/1.1\" 500 51 \"http://localhost:3001/\" \"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-26 03:54:14"}
{"error":"Table 'school_management_system.grade_levels' doesn't exist","level":"error","message":"Database query error:","params":[],"query":"\n      SELECT COUNT(*) as total\n      FROM students s\n      LEFT JOIN users u ON s.user_id = u.id\n      LEFT JOIN classes c ON s.current_class_id = c.id\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      WHERE 1=1\n    ","service":"sms-backend","timestamp":"2025-07-26 03:54:14"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Get students error: Table 'school_management_system.grade_levels' doesn't exist","service":"sms-backend","sql":"\n      SELECT COUNT(*) as total\n      FROM students s\n      LEFT JOIN users u ON s.user_id = u.id\n      LEFT JOIN classes c ON s.current_class_id = c.id\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      WHERE 1=1\n    ","sqlMessage":"Table 'school_management_system.grade_levels' doesn't exist","sqlState":"42S02","stack":"Error: Table 'school_management_system.grade_levels' doesn't exist\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\config\\database.js:48:31)\n    at getStudents (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\studentController.js:1076:31)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at handleValidationErrors (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\utils\\validation.js:21:3)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\route.js:149:13)","timestamp":"2025-07-26 03:54:15"}
{"level":"info","message":"::1 - - [26/Jul/2025:14:54:15 +0000] \"GET /api/students?page=1&limit=10&search=&sort_by=first_name&sort_order=ASC HTTP/1.1\" 500 57 \"http://localhost:3001/\" \"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-26 03:54:15"}
{"error":"Table 'school_management_system.grade_levels' doesn't exist","level":"error","message":"Database query error:","params":[],"query":"\n      SELECT COUNT(*) as total\n      FROM students s\n      LEFT JOIN users u ON s.user_id = u.id\n      LEFT JOIN classes c ON s.current_class_id = c.id\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      WHERE 1=1\n    ","service":"sms-backend","timestamp":"2025-07-26 03:54:15"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Get students error: Table 'school_management_system.grade_levels' doesn't exist","service":"sms-backend","sql":"\n      SELECT COUNT(*) as total\n      FROM students s\n      LEFT JOIN users u ON s.user_id = u.id\n      LEFT JOIN classes c ON s.current_class_id = c.id\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      WHERE 1=1\n    ","sqlMessage":"Table 'school_management_system.grade_levels' doesn't exist","sqlState":"42S02","stack":"Error: Table 'school_management_system.grade_levels' doesn't exist\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\config\\database.js:48:31)\n    at getStudents (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\studentController.js:1076:31)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at handleValidationErrors (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\utils\\validation.js:21:3)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\route.js:149:13)","timestamp":"2025-07-26 03:54:15"}
{"level":"info","message":"::1 - - [26/Jul/2025:14:54:15 +0000] \"GET /api/students?page=1&limit=10&search=&sort_by=first_name&sort_order=ASC HTTP/1.1\" 500 57 \"http://localhost:3001/\" \"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-26 03:54:15"}
{"error":"Table 'school_management_system.admins' doesn't exist","level":"error","message":"Database query error:","params":[2],"query":"SELECT * FROM admins WHERE user_id = ?","service":"sms-backend","timestamp":"2025-07-26 03:56:23"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Get profile error: Table 'school_management_system.admins' doesn't exist","service":"sms-backend","sql":"SELECT * FROM admins WHERE user_id = ?","sqlMessage":"Table 'school_management_system.admins' doesn't exist","sqlState":"42S02","stack":"Error: Table 'school_management_system.admins' doesn't exist\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\config\\database.js:48:31)\n    at getProfile (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\authController.js:256:30)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-26 03:56:23"}
{"level":"info","message":"::1 - - [26/Jul/2025:14:56:23 +0000] \"GET /api/auth/profile HTTP/1.1\" 500 51 \"http://localhost:3001/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-26 03:56:23"}
{"error":"Table 'school_management_system.admins' doesn't exist","level":"error","message":"Database query error:","params":[2],"query":"SELECT * FROM admins WHERE user_id = ?","service":"sms-backend","timestamp":"2025-07-26 03:56:29"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Get profile error: Table 'school_management_system.admins' doesn't exist","service":"sms-backend","sql":"SELECT * FROM admins WHERE user_id = ?","sqlMessage":"Table 'school_management_system.admins' doesn't exist","sqlState":"42S02","stack":"Error: Table 'school_management_system.admins' doesn't exist\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\config\\database.js:48:31)\n    at getProfile (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\authController.js:256:30)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-26 03:56:29"}
{"level":"info","message":"::1 - - [26/Jul/2025:14:56:29 +0000] \"GET /api/auth/profile HTTP/1.1\" 500 51 \"http://localhost:3001/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-26 03:56:29"}
{"error":"Table 'school_management_system.grade_levels' doesn't exist","level":"error","message":"Database query error:","params":[],"query":"\n      SELECT COUNT(*) as total\n      FROM students s\n      LEFT JOIN users u ON s.user_id = u.id\n      LEFT JOIN classes c ON s.current_class_id = c.id\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      WHERE 1=1\n    ","service":"sms-backend","timestamp":"2025-07-26 03:56:29"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Get students error: Table 'school_management_system.grade_levels' doesn't exist","service":"sms-backend","sql":"\n      SELECT COUNT(*) as total\n      FROM students s\n      LEFT JOIN users u ON s.user_id = u.id\n      LEFT JOIN classes c ON s.current_class_id = c.id\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      WHERE 1=1\n    ","sqlMessage":"Table 'school_management_system.grade_levels' doesn't exist","sqlState":"42S02","stack":"Error: Table 'school_management_system.grade_levels' doesn't exist\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\config\\database.js:48:31)\n    at getStudents (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\studentController.js:1076:31)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at handleValidationErrors (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\utils\\validation.js:21:3)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\route.js:149:13)","timestamp":"2025-07-26 03:56:29"}
{"level":"info","message":"::1 - - [26/Jul/2025:14:56:29 +0000] \"GET /api/students?page=1&limit=10&search=&sort_by=first_name&sort_order=ASC HTTP/1.1\" 500 57 \"http://localhost:3001/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-26 03:56:29"}
{"error":"Table 'school_management_system.grade_levels' doesn't exist","level":"error","message":"Database query error:","params":[],"query":"\n      SELECT COUNT(*) as total\n      FROM students s\n      LEFT JOIN users u ON s.user_id = u.id\n      LEFT JOIN classes c ON s.current_class_id = c.id\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      WHERE 1=1\n    ","service":"sms-backend","timestamp":"2025-07-26 03:56:29"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Get students error: Table 'school_management_system.grade_levels' doesn't exist","service":"sms-backend","sql":"\n      SELECT COUNT(*) as total\n      FROM students s\n      LEFT JOIN users u ON s.user_id = u.id\n      LEFT JOIN classes c ON s.current_class_id = c.id\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      WHERE 1=1\n    ","sqlMessage":"Table 'school_management_system.grade_levels' doesn't exist","sqlState":"42S02","stack":"Error: Table 'school_management_system.grade_levels' doesn't exist\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\config\\database.js:48:31)\n    at getStudents (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\studentController.js:1076:31)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at handleValidationErrors (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\utils\\validation.js:21:3)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\route.js:149:13)","timestamp":"2025-07-26 03:56:29"}
{"level":"info","message":"::1 - - [26/Jul/2025:14:56:29 +0000] \"GET /api/students?page=1&limit=10&search=&sort_by=first_name&sort_order=ASC HTTP/1.1\" 500 57 \"http://localhost:3001/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-26 03:56:29"}
{"error":"Table 'school_management_system.admins' doesn't exist","level":"error","message":"Database query error:","params":[2],"query":"SELECT * FROM admins WHERE user_id = ?","service":"sms-backend","timestamp":"2025-07-26 03:56:48"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Get profile error: Table 'school_management_system.admins' doesn't exist","service":"sms-backend","sql":"SELECT * FROM admins WHERE user_id = ?","sqlMessage":"Table 'school_management_system.admins' doesn't exist","sqlState":"42S02","stack":"Error: Table 'school_management_system.admins' doesn't exist\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\config\\database.js:48:31)\n    at getProfile (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\authController.js:256:30)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-26 03:56:48"}
{"level":"info","message":"::1 - - [26/Jul/2025:14:56:48 +0000] \"GET /api/auth/profile HTTP/1.1\" 500 51 \"http://localhost:3001/\" \"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-26 03:56:48"}
{"error":"Table 'school_management_system.grade_levels' doesn't exist","level":"error","message":"Database query error:","params":[],"query":"\n      SELECT COUNT(*) as total\n      FROM students s\n      LEFT JOIN users u ON s.user_id = u.id\n      LEFT JOIN classes c ON s.current_class_id = c.id\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      WHERE 1=1\n    ","service":"sms-backend","timestamp":"2025-07-26 03:56:48"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Get students error: Table 'school_management_system.grade_levels' doesn't exist","service":"sms-backend","sql":"\n      SELECT COUNT(*) as total\n      FROM students s\n      LEFT JOIN users u ON s.user_id = u.id\n      LEFT JOIN classes c ON s.current_class_id = c.id\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      WHERE 1=1\n    ","sqlMessage":"Table 'school_management_system.grade_levels' doesn't exist","sqlState":"42S02","stack":"Error: Table 'school_management_system.grade_levels' doesn't exist\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\config\\database.js:48:31)\n    at getStudents (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\studentController.js:1076:31)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at handleValidationErrors (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\utils\\validation.js:21:3)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\route.js:149:13)","timestamp":"2025-07-26 03:56:48"}
{"level":"info","message":"::1 - - [26/Jul/2025:14:56:48 +0000] \"GET /api/students?page=1&limit=10&search=&sort_by=first_name&sort_order=ASC HTTP/1.1\" 500 57 \"http://localhost:3001/\" \"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-26 03:56:48"}
{"error":"Table 'school_management_system.grade_levels' doesn't exist","level":"error","message":"Database query error:","params":[],"query":"\n      SELECT COUNT(*) as total\n      FROM students s\n      LEFT JOIN users u ON s.user_id = u.id\n      LEFT JOIN classes c ON s.current_class_id = c.id\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      WHERE 1=1\n    ","service":"sms-backend","timestamp":"2025-07-26 03:56:48"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Get students error: Table 'school_management_system.grade_levels' doesn't exist","service":"sms-backend","sql":"\n      SELECT COUNT(*) as total\n      FROM students s\n      LEFT JOIN users u ON s.user_id = u.id\n      LEFT JOIN classes c ON s.current_class_id = c.id\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      WHERE 1=1\n    ","sqlMessage":"Table 'school_management_system.grade_levels' doesn't exist","sqlState":"42S02","stack":"Error: Table 'school_management_system.grade_levels' doesn't exist\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\config\\database.js:48:31)\n    at getStudents (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\studentController.js:1076:31)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at handleValidationErrors (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\utils\\validation.js:21:3)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\route.js:149:13)","timestamp":"2025-07-26 03:56:48"}
{"level":"info","message":"::1 - - [26/Jul/2025:14:56:48 +0000] \"GET /api/students?page=1&limit=10&search=&sort_by=first_name&sort_order=ASC HTTP/1.1\" 500 57 \"http://localhost:3001/\" \"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-26 03:56:48"}
{"error":"Table 'school_management_system.admins' doesn't exist","level":"error","message":"Database query error:","params":[2],"query":"SELECT * FROM admins WHERE user_id = ?","service":"sms-backend","timestamp":"2025-07-26 03:57:03"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Get profile error: Table 'school_management_system.admins' doesn't exist","service":"sms-backend","sql":"SELECT * FROM admins WHERE user_id = ?","sqlMessage":"Table 'school_management_system.admins' doesn't exist","sqlState":"42S02","stack":"Error: Table 'school_management_system.admins' doesn't exist\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\config\\database.js:48:31)\n    at getProfile (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\authController.js:256:30)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-26 03:57:03"}
{"level":"info","message":"::1 - - [26/Jul/2025:14:57:03 +0000] \"GET /api/auth/profile HTTP/1.1\" 500 51 \"http://localhost:3001/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-26 03:57:03"}
{"error":"Table 'school_management_system.grade_levels' doesn't exist","level":"error","message":"Database query error:","params":[],"query":"\n      SELECT COUNT(*) as total\n      FROM students s\n      LEFT JOIN users u ON s.user_id = u.id\n      LEFT JOIN classes c ON s.current_class_id = c.id\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      WHERE 1=1\n    ","service":"sms-backend","timestamp":"2025-07-26 03:57:04"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Get students error: Table 'school_management_system.grade_levels' doesn't exist","service":"sms-backend","sql":"\n      SELECT COUNT(*) as total\n      FROM students s\n      LEFT JOIN users u ON s.user_id = u.id\n      LEFT JOIN classes c ON s.current_class_id = c.id\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      WHERE 1=1\n    ","sqlMessage":"Table 'school_management_system.grade_levels' doesn't exist","sqlState":"42S02","stack":"Error: Table 'school_management_system.grade_levels' doesn't exist\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\config\\database.js:48:31)\n    at getStudents (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\studentController.js:1076:31)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at handleValidationErrors (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\utils\\validation.js:21:3)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\route.js:149:13)","timestamp":"2025-07-26 03:57:04"}
{"level":"info","message":"::1 - - [26/Jul/2025:14:57:04 +0000] \"GET /api/students?page=1&limit=10&search=&sort_by=first_name&sort_order=ASC HTTP/1.1\" 500 57 \"http://localhost:3001/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-26 03:57:04"}
{"error":"Table 'school_management_system.grade_levels' doesn't exist","level":"error","message":"Database query error:","params":[],"query":"\n      SELECT COUNT(*) as total\n      FROM students s\n      LEFT JOIN users u ON s.user_id = u.id\n      LEFT JOIN classes c ON s.current_class_id = c.id\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      WHERE 1=1\n    ","service":"sms-backend","timestamp":"2025-07-26 03:57:04"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Get students error: Table 'school_management_system.grade_levels' doesn't exist","service":"sms-backend","sql":"\n      SELECT COUNT(*) as total\n      FROM students s\n      LEFT JOIN users u ON s.user_id = u.id\n      LEFT JOIN classes c ON s.current_class_id = c.id\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      WHERE 1=1\n    ","sqlMessage":"Table 'school_management_system.grade_levels' doesn't exist","sqlState":"42S02","stack":"Error: Table 'school_management_system.grade_levels' doesn't exist\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\config\\database.js:48:31)\n    at getStudents (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\studentController.js:1076:31)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at handleValidationErrors (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\utils\\validation.js:21:3)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\route.js:149:13)","timestamp":"2025-07-26 03:57:04"}
{"level":"info","message":"::1 - - [26/Jul/2025:14:57:04 +0000] \"GET /api/students?page=1&limit=10&search=&sort_by=first_name&sort_order=ASC HTTP/1.1\" 500 57 \"http://localhost:3001/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-26 03:57:04"}
{"error":"Table 'school_management_system.admins' doesn't exist","level":"error","message":"Database query error:","params":[2],"query":"SELECT * FROM admins WHERE user_id = ?","service":"sms-backend","timestamp":"2025-07-26 03:57:21"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Get profile error: Table 'school_management_system.admins' doesn't exist","service":"sms-backend","sql":"SELECT * FROM admins WHERE user_id = ?","sqlMessage":"Table 'school_management_system.admins' doesn't exist","sqlState":"42S02","stack":"Error: Table 'school_management_system.admins' doesn't exist\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\config\\database.js:48:31)\n    at getProfile (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\authController.js:256:30)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-26 03:57:21"}
{"level":"info","message":"::1 - - [26/Jul/2025:14:57:21 +0000] \"GET /api/auth/profile HTTP/1.1\" 500 51 \"http://localhost:3001/\" \"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-26 03:57:21"}
{"error":"Table 'school_management_system.grade_levels' doesn't exist","level":"error","message":"Database query error:","params":[],"query":"\n      SELECT COUNT(*) as total\n      FROM students s\n      LEFT JOIN users u ON s.user_id = u.id\n      LEFT JOIN classes c ON s.current_class_id = c.id\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      WHERE 1=1\n    ","service":"sms-backend","timestamp":"2025-07-26 03:57:21"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Get students error: Table 'school_management_system.grade_levels' doesn't exist","service":"sms-backend","sql":"\n      SELECT COUNT(*) as total\n      FROM students s\n      LEFT JOIN users u ON s.user_id = u.id\n      LEFT JOIN classes c ON s.current_class_id = c.id\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      WHERE 1=1\n    ","sqlMessage":"Table 'school_management_system.grade_levels' doesn't exist","sqlState":"42S02","stack":"Error: Table 'school_management_system.grade_levels' doesn't exist\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\config\\database.js:48:31)\n    at getStudents (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\studentController.js:1076:31)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at handleValidationErrors (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\utils\\validation.js:21:3)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\route.js:149:13)","timestamp":"2025-07-26 03:57:21"}
{"level":"info","message":"::1 - - [26/Jul/2025:14:57:21 +0000] \"GET /api/students?page=1&limit=10&search=&sort_by=first_name&sort_order=ASC HTTP/1.1\" 500 57 \"http://localhost:3001/\" \"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-26 03:57:21"}
{"error":"Table 'school_management_system.grade_levels' doesn't exist","level":"error","message":"Database query error:","params":[],"query":"\n      SELECT COUNT(*) as total\n      FROM students s\n      LEFT JOIN users u ON s.user_id = u.id\n      LEFT JOIN classes c ON s.current_class_id = c.id\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      WHERE 1=1\n    ","service":"sms-backend","timestamp":"2025-07-26 03:57:21"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Get students error: Table 'school_management_system.grade_levels' doesn't exist","service":"sms-backend","sql":"\n      SELECT COUNT(*) as total\n      FROM students s\n      LEFT JOIN users u ON s.user_id = u.id\n      LEFT JOIN classes c ON s.current_class_id = c.id\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      WHERE 1=1\n    ","sqlMessage":"Table 'school_management_system.grade_levels' doesn't exist","sqlState":"42S02","stack":"Error: Table 'school_management_system.grade_levels' doesn't exist\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\config\\database.js:48:31)\n    at getStudents (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\studentController.js:1076:31)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at handleValidationErrors (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\utils\\validation.js:21:3)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\route.js:149:13)","timestamp":"2025-07-26 03:57:21"}
{"level":"info","message":"::1 - - [26/Jul/2025:14:57:21 +0000] \"GET /api/students?page=1&limit=10&search=&sort_by=first_name&sort_order=ASC HTTP/1.1\" 500 57 \"http://localhost:3001/\" \"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-26 03:57:21"}
{"code":"ER_ACCESS_DENIED_ERROR","errno":1045,"level":"error","message":"Database connection failed: Access denied for user 'root'@'localhost' (using password: YES)","service":"sms-backend","sqlMessage":"Access denied for user 'root'@'localhost' (using password: YES)","sqlState":"28000","stack":"Error: Access denied for user 'root'@'localhost' (using password: YES)\n    at Packet.asError (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\packets\\packet.js:740:17)\n    at ClientHandshake.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\commands\\command.js:29:26)\n    at PoolConnection.handlePacket (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\base\\connection.js:475:34)\n    at PacketParser.onPacket (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\base\\connection.js:93:12)\n    at PacketParser.executeStart (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\packet_parser.js:75:16)\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\base\\connection.js:100:25)\n    at Socket.emit (node:events:519:28)\n    at addChunk (node:internal/streams/readable:559:12)\n    at readableAddChunkPushByteMode (node:internal/streams/readable:510:3)\n    at Readable.push (node:internal/streams/readable:390:5)","timestamp":"2025-07-26 04:02:07"}
{"code":"ER_ACCESS_DENIED_ERROR","errno":1045,"level":"error","message":"Database connection failed: Access denied for user 'root'@'localhost' (using password: YES)","service":"sms-backend","sqlMessage":"Access denied for user 'root'@'localhost' (using password: YES)","sqlState":"28000","stack":"Error: Access denied for user 'root'@'localhost' (using password: YES)\n    at Packet.asError (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\packets\\packet.js:740:17)\n    at ClientHandshake.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\commands\\command.js:29:26)\n    at PoolConnection.handlePacket (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\base\\connection.js:475:34)\n    at PacketParser.onPacket (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\base\\connection.js:93:12)\n    at PacketParser.executeStart (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\packet_parser.js:75:16)\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\base\\connection.js:100:25)\n    at Socket.emit (node:events:519:28)\n    at addChunk (node:internal/streams/readable:559:12)\n    at readableAddChunkPushByteMode (node:internal/streams/readable:510:3)\n    at Readable.push (node:internal/streams/readable:390:5)","timestamp":"2025-07-26 04:02:08"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-26 04:03:10"}
{"level":"info","message":"Database connected successfully","service":"sms-backend","timestamp":"2025-07-26 04:03:10"}
{"level":"info","message":"Server running on port 5000 in development mode","service":"sms-backend","timestamp":"2025-07-26 04:03:10"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-26 04:03:10"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-26 04:03:17"}
{"error":"Unknown column 'grade_level_id' in 'field list'","level":"error","message":"Database query error:","params":["Class 1A",1,"Grade 1 Section A"],"query":"INSERT IGNORE INTO classes (name, grade_level_id, description) VALUES (?, ?, ?)","service":"sms-backend","timestamp":"2025-07-26 04:03:18"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-26 04:03:52"}
{"level":"info","message":"Database connected successfully","service":"sms-backend","timestamp":"2025-07-26 04:03:52"}
{"level":"info","message":"Server running on port 5000 in development mode","service":"sms-backend","timestamp":"2025-07-26 04:03:52"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-26 04:03:54"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-26 04:04:04"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-26 04:04:43"}
{"level":"info","message":"Database connected successfully","service":"sms-backend","timestamp":"2025-07-26 04:04:43"}
{"level":"info","message":"Server running on port 5000 in development mode","service":"sms-backend","timestamp":"2025-07-26 04:04:43"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-26 04:04:43"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-26 04:04:58"}
{"level":"info","message":"Database connected successfully","service":"sms-backend","timestamp":"2025-07-26 04:04:58"}
{"level":"info","message":"Server running on port 5000 in development mode","service":"sms-backend","timestamp":"2025-07-26 04:04:58"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-26 04:04:58"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-26 04:05:11"}
{"level":"info","message":"Database connected successfully","service":"sms-backend","timestamp":"2025-07-26 04:05:11"}
{"level":"info","message":"Server running on port 5000 in development mode","service":"sms-backend","timestamp":"2025-07-26 04:05:11"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-26 04:05:11"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-26 04:05:30"}
{"level":"info","message":"Database connected successfully","service":"sms-backend","timestamp":"2025-07-26 04:05:30"}
{"level":"info","message":"Server running on port 5000 in development mode","service":"sms-backend","timestamp":"2025-07-26 04:05:30"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-26 04:05:31"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-26 04:05:39"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-26 04:06:02"}
{"level":"info","message":"Database connected successfully","service":"sms-backend","timestamp":"2025-07-26 04:06:02"}
{"level":"info","message":"Server running on port 5000 in development mode","service":"sms-backend","timestamp":"2025-07-26 04:06:02"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-26 04:06:02"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-26 04:06:16"}
{"level":"info","message":"Database connected successfully","service":"sms-backend","timestamp":"2025-07-26 04:06:16"}
{"level":"info","message":"Server running on port 5000 in development mode","service":"sms-backend","timestamp":"2025-07-26 04:06:16"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-26 04:06:16"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-26 04:06:27"}
{"level":"info","message":"Database connected successfully","service":"sms-backend","timestamp":"2025-07-26 04:06:27"}
{"level":"info","message":"Server running on port 5000 in development mode","service":"sms-backend","timestamp":"2025-07-26 04:06:27"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-26 04:07:21"}
{"level":"info","message":"Database connected successfully","service":"sms-backend","timestamp":"2025-07-26 04:07:21"}
{"level":"info","message":"Server running on port 5000 in development mode","service":"sms-backend","timestamp":"2025-07-26 04:07:21"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-26 04:07:21"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-26 04:07:34"}
{"level":"info","message":"Database connected successfully","service":"sms-backend","timestamp":"2025-07-26 04:07:34"}
{"level":"info","message":"Server running on port 5000 in development mode","service":"sms-backend","timestamp":"2025-07-26 04:07:34"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-26 04:07:34"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-26 04:07:50"}
{"level":"info","message":"Database connected successfully","service":"sms-backend","timestamp":"2025-07-26 04:07:50"}
{"level":"info","message":"Server running on port 5000 in development mode","service":"sms-backend","timestamp":"2025-07-26 04:07:50"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-26 04:07:50"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-26 04:09:45"}
{"level":"info","message":"Database connected successfully","service":"sms-backend","timestamp":"2025-07-26 04:09:45"}
{"level":"info","message":"Server running on port 5000 in development mode","service":"sms-backend","timestamp":"2025-07-26 04:09:45"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-26 04:09:45"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-26 04:09:58"}
{"level":"info","message":"Database connected successfully","service":"sms-backend","timestamp":"2025-07-26 04:09:58"}
{"level":"info","message":"Server running on port 5000 in development mode","service":"sms-backend","timestamp":"2025-07-26 04:09:58"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-26 04:09:58"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-26 04:10:12"}
{"level":"info","message":"Database connected successfully","service":"sms-backend","timestamp":"2025-07-26 04:10:12"}
{"level":"info","message":"Server running on port 5000 in development mode","service":"sms-backend","timestamp":"2025-07-26 04:10:12"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-26 04:10:12"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-26 04:10:38"}
{"level":"info","message":"Database connected successfully","service":"sms-backend","timestamp":"2025-07-26 04:10:38"}
{"level":"info","message":"Server running on port 5000 in development mode","service":"sms-backend","timestamp":"2025-07-26 04:10:38"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-26 04:10:38"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-26 04:10:53"}
{"level":"info","message":"Database connected successfully","service":"sms-backend","timestamp":"2025-07-26 04:10:53"}
{"level":"info","message":"Server running on port 5000 in development mode","service":"sms-backend","timestamp":"2025-07-26 04:10:53"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-26 04:10:53"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-26 04:11:09"}
{"level":"info","message":"Database connected successfully","service":"sms-backend","timestamp":"2025-07-26 04:11:09"}
{"level":"info","message":"Server running on port 5000 in development mode","service":"sms-backend","timestamp":"2025-07-26 04:11:09"}
{"error":"Table 'school_management_system.admins' doesn't exist","level":"error","message":"Database query error:","params":[2],"query":"SELECT * FROM admins WHERE user_id = ?","service":"sms-backend","timestamp":"2025-07-26 04:11:38"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Get profile error: Table 'school_management_system.admins' doesn't exist","service":"sms-backend","sql":"SELECT * FROM admins WHERE user_id = ?","sqlMessage":"Table 'school_management_system.admins' doesn't exist","sqlState":"42S02","stack":"Error: Table 'school_management_system.admins' doesn't exist\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\config\\database.js:48:31)\n    at getProfile (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\authController.js:256:30)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-26 04:11:38"}
{"level":"info","message":"::1 - - [26/Jul/2025:15:11:38 +0000] \"GET /api/auth/profile HTTP/1.1\" 500 51 \"http://localhost:3001/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-26 04:11:38"}
{"error":"Unknown column 's.first_name' in 'field list'","level":"error","message":"Database query error:","params":[10,0],"query":"\n      SELECT \n        s.id,\n        s.student_id,\n        s.first_name,\n        s.last_name,\n        s.middle_name,\n        s.date_of_birth,\n        s.gender,\n        s.phone,\n        s.address,\n        s.admission_date,\n        s.passport_photo,\n        s.status,\n        s.created_at,\n        u.email,\n        c.name as class_name,\n        gl.name as grade_level,\n        ay.name as academic_year,\n        CONCAT(s.first_name, ' ', s.last_name) as full_name\n      FROM students s\n      LEFT JOIN users u ON s.user_id = u.id\n      LEFT JOIN classes c ON s.class_id = c.id\n      LEFT JOIN grade_levels gl ON c.grade_level = gl.name\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      WHERE 1=1\n      ORDER BY s.first_name ASC\n      LIMIT ? OFFSET ?\n    ","service":"sms-backend","timestamp":"2025-07-26 04:11:38"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Get students error: Unknown column 's.first_name' in 'field list'","service":"sms-backend","sql":"\n      SELECT \n        s.id,\n        s.student_id,\n        s.first_name,\n        s.last_name,\n        s.middle_name,\n        s.date_of_birth,\n        s.gender,\n        s.phone,\n        s.address,\n        s.admission_date,\n        s.passport_photo,\n        s.status,\n        s.created_at,\n        u.email,\n        c.name as class_name,\n        gl.name as grade_level,\n        ay.name as academic_year,\n        CONCAT(s.first_name, ' ', s.last_name) as full_name\n      FROM students s\n      LEFT JOIN users u ON s.user_id = u.id\n      LEFT JOIN classes c ON s.class_id = c.id\n      LEFT JOIN grade_levels gl ON c.grade_level = gl.name\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      WHERE 1=1\n      ORDER BY s.first_name ASC\n      LIMIT ? OFFSET ?\n    ","sqlMessage":"Unknown column 's.first_name' in 'field list'","sqlState":"42S22","stack":"Error: Unknown column 's.first_name' in 'field list'\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\config\\database.js:48:31)\n    at getStudents (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\studentController.js:1110:28)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-26 04:11:38"}
{"level":"info","message":"::1 - - [26/Jul/2025:15:11:38 +0000] \"GET /api/students?page=1&limit=10&search=&sort_by=first_name&sort_order=ASC HTTP/1.1\" 500 57 \"http://localhost:3001/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-26 04:11:38"}
{"error":"Unknown column 's.first_name' in 'field list'","level":"error","message":"Database query error:","params":[10,0],"query":"\n      SELECT \n        s.id,\n        s.student_id,\n        s.first_name,\n        s.last_name,\n        s.middle_name,\n        s.date_of_birth,\n        s.gender,\n        s.phone,\n        s.address,\n        s.admission_date,\n        s.passport_photo,\n        s.status,\n        s.created_at,\n        u.email,\n        c.name as class_name,\n        gl.name as grade_level,\n        ay.name as academic_year,\n        CONCAT(s.first_name, ' ', s.last_name) as full_name\n      FROM students s\n      LEFT JOIN users u ON s.user_id = u.id\n      LEFT JOIN classes c ON s.class_id = c.id\n      LEFT JOIN grade_levels gl ON c.grade_level = gl.name\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      WHERE 1=1\n      ORDER BY s.first_name ASC\n      LIMIT ? OFFSET ?\n    ","service":"sms-backend","timestamp":"2025-07-26 04:11:38"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Get students error: Unknown column 's.first_name' in 'field list'","service":"sms-backend","sql":"\n      SELECT \n        s.id,\n        s.student_id,\n        s.first_name,\n        s.last_name,\n        s.middle_name,\n        s.date_of_birth,\n        s.gender,\n        s.phone,\n        s.address,\n        s.admission_date,\n        s.passport_photo,\n        s.status,\n        s.created_at,\n        u.email,\n        c.name as class_name,\n        gl.name as grade_level,\n        ay.name as academic_year,\n        CONCAT(s.first_name, ' ', s.last_name) as full_name\n      FROM students s\n      LEFT JOIN users u ON s.user_id = u.id\n      LEFT JOIN classes c ON s.class_id = c.id\n      LEFT JOIN grade_levels gl ON c.grade_level = gl.name\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      WHERE 1=1\n      ORDER BY s.first_name ASC\n      LIMIT ? OFFSET ?\n    ","sqlMessage":"Unknown column 's.first_name' in 'field list'","sqlState":"42S22","stack":"Error: Unknown column 's.first_name' in 'field list'\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\config\\database.js:48:31)\n    at getStudents (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\studentController.js:1110:28)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-26 04:11:38"}
{"level":"info","message":"::1 - - [26/Jul/2025:15:11:38 +0000] \"GET /api/students?page=1&limit=10&search=&sort_by=first_name&sort_order=ASC HTTP/1.1\" 500 57 \"http://localhost:3001/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-26 04:11:38"}
{"error":"Table 'school_management_system.admins' doesn't exist","level":"error","message":"Database query error:","params":[2],"query":"SELECT * FROM admins WHERE user_id = ?","service":"sms-backend","timestamp":"2025-07-26 04:24:59"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Get profile error: Table 'school_management_system.admins' doesn't exist","service":"sms-backend","sql":"SELECT * FROM admins WHERE user_id = ?","sqlMessage":"Table 'school_management_system.admins' doesn't exist","sqlState":"42S02","stack":"Error: Table 'school_management_system.admins' doesn't exist\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\config\\database.js:48:31)\n    at getProfile (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\authController.js:256:30)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-26 04:24:59"}
{"level":"info","message":"::1 - - [26/Jul/2025:15:24:59 +0000] \"GET /api/auth/profile HTTP/1.1\" 500 51 \"http://localhost:3001/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-26 04:24:59"}
{"error":"Unknown column 's.first_name' in 'field list'","level":"error","message":"Database query error:","params":[10,0],"query":"\n      SELECT \n        s.id,\n        s.student_id,\n        s.first_name,\n        s.last_name,\n        s.middle_name,\n        s.date_of_birth,\n        s.gender,\n        s.phone,\n        s.address,\n        s.admission_date,\n        s.passport_photo,\n        s.status,\n        s.created_at,\n        u.email,\n        c.name as class_name,\n        gl.name as grade_level,\n        ay.name as academic_year,\n        CONCAT(s.first_name, ' ', s.last_name) as full_name\n      FROM students s\n      LEFT JOIN users u ON s.user_id = u.id\n      LEFT JOIN classes c ON s.class_id = c.id\n      LEFT JOIN grade_levels gl ON c.grade_level = gl.name\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      WHERE 1=1\n      ORDER BY s.first_name ASC\n      LIMIT ? OFFSET ?\n    ","service":"sms-backend","timestamp":"2025-07-26 04:24:59"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Get students error: Unknown column 's.first_name' in 'field list'","service":"sms-backend","sql":"\n      SELECT \n        s.id,\n        s.student_id,\n        s.first_name,\n        s.last_name,\n        s.middle_name,\n        s.date_of_birth,\n        s.gender,\n        s.phone,\n        s.address,\n        s.admission_date,\n        s.passport_photo,\n        s.status,\n        s.created_at,\n        u.email,\n        c.name as class_name,\n        gl.name as grade_level,\n        ay.name as academic_year,\n        CONCAT(s.first_name, ' ', s.last_name) as full_name\n      FROM students s\n      LEFT JOIN users u ON s.user_id = u.id\n      LEFT JOIN classes c ON s.class_id = c.id\n      LEFT JOIN grade_levels gl ON c.grade_level = gl.name\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      WHERE 1=1\n      ORDER BY s.first_name ASC\n      LIMIT ? OFFSET ?\n    ","sqlMessage":"Unknown column 's.first_name' in 'field list'","sqlState":"42S22","stack":"Error: Unknown column 's.first_name' in 'field list'\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\config\\database.js:48:31)\n    at getStudents (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\studentController.js:1110:28)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-26 04:24:59"}
{"level":"info","message":"::1 - - [26/Jul/2025:15:24:59 +0000] \"GET /api/students?page=1&limit=10&search=&sort_by=first_name&sort_order=ASC HTTP/1.1\" 500 57 \"http://localhost:3001/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-26 04:24:59"}
{"error":"Unknown column 's.first_name' in 'field list'","level":"error","message":"Database query error:","params":[10,0],"query":"\n      SELECT \n        s.id,\n        s.student_id,\n        s.first_name,\n        s.last_name,\n        s.middle_name,\n        s.date_of_birth,\n        s.gender,\n        s.phone,\n        s.address,\n        s.admission_date,\n        s.passport_photo,\n        s.status,\n        s.created_at,\n        u.email,\n        c.name as class_name,\n        gl.name as grade_level,\n        ay.name as academic_year,\n        CONCAT(s.first_name, ' ', s.last_name) as full_name\n      FROM students s\n      LEFT JOIN users u ON s.user_id = u.id\n      LEFT JOIN classes c ON s.class_id = c.id\n      LEFT JOIN grade_levels gl ON c.grade_level = gl.name\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      WHERE 1=1\n      ORDER BY s.first_name ASC\n      LIMIT ? OFFSET ?\n    ","service":"sms-backend","timestamp":"2025-07-26 04:24:59"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Get students error: Unknown column 's.first_name' in 'field list'","service":"sms-backend","sql":"\n      SELECT \n        s.id,\n        s.student_id,\n        s.first_name,\n        s.last_name,\n        s.middle_name,\n        s.date_of_birth,\n        s.gender,\n        s.phone,\n        s.address,\n        s.admission_date,\n        s.passport_photo,\n        s.status,\n        s.created_at,\n        u.email,\n        c.name as class_name,\n        gl.name as grade_level,\n        ay.name as academic_year,\n        CONCAT(s.first_name, ' ', s.last_name) as full_name\n      FROM students s\n      LEFT JOIN users u ON s.user_id = u.id\n      LEFT JOIN classes c ON s.class_id = c.id\n      LEFT JOIN grade_levels gl ON c.grade_level = gl.name\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      WHERE 1=1\n      ORDER BY s.first_name ASC\n      LIMIT ? OFFSET ?\n    ","sqlMessage":"Unknown column 's.first_name' in 'field list'","sqlState":"42S22","stack":"Error: Unknown column 's.first_name' in 'field list'\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\config\\database.js:48:31)\n    at getStudents (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\studentController.js:1110:28)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-26 04:24:59"}
{"level":"info","message":"::1 - - [26/Jul/2025:15:24:59 +0000] \"GET /api/students?page=1&limit=10&search=&sort_by=first_name&sort_order=ASC HTTP/1.1\" 500 57 \"http://localhost:3001/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-26 04:24:59"}
{"level":"info","message":"SIGINT received. Shutting down gracefully...","service":"sms-backend","timestamp":"2025-07-26 04:25:21"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-26 04:25:30"}
{"level":"info","message":"Database connected successfully","service":"sms-backend","timestamp":"2025-07-26 04:25:30"}
{"level":"info","message":"Server running on port 5000 in development mode","service":"sms-backend","timestamp":"2025-07-26 04:25:30"}
{"error":"Table 'school_management_system.admins' doesn't exist","level":"error","message":"Database query error:","params":[2],"query":"SELECT * FROM admins WHERE user_id = ?","service":"sms-backend","timestamp":"2025-07-26 04:25:35"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Get profile error: Table 'school_management_system.admins' doesn't exist","service":"sms-backend","sql":"SELECT * FROM admins WHERE user_id = ?","sqlMessage":"Table 'school_management_system.admins' doesn't exist","sqlState":"42S02","stack":"Error: Table 'school_management_system.admins' doesn't exist\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\config\\database.js:48:31)\n    at getProfile (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\authController.js:256:30)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-26 04:25:35"}
{"level":"info","message":"::1 - - [26/Jul/2025:15:25:35 +0000] \"GET /api/auth/profile HTTP/1.1\" 500 51 \"http://localhost:3001/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-26 04:25:35"}
{"error":"Unknown column 's.first_name' in 'field list'","level":"error","message":"Database query error:","params":[10,0],"query":"\n      SELECT \n        s.id,\n        s.student_id,\n        s.first_name,\n        s.last_name,\n        s.middle_name,\n        s.date_of_birth,\n        s.gender,\n        s.phone,\n        s.address,\n        s.admission_date,\n        s.passport_photo,\n        s.status,\n        s.created_at,\n        u.email,\n        c.name as class_name,\n        gl.name as grade_level,\n        ay.name as academic_year,\n        CONCAT(s.first_name, ' ', s.last_name) as full_name\n      FROM students s\n      LEFT JOIN users u ON s.user_id = u.id\n      LEFT JOIN classes c ON s.class_id = c.id\n      LEFT JOIN grade_levels gl ON c.grade_level = gl.name\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      WHERE 1=1\n      ORDER BY s.first_name ASC\n      LIMIT ? OFFSET ?\n    ","service":"sms-backend","timestamp":"2025-07-26 04:25:35"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Get students error: Unknown column 's.first_name' in 'field list'","service":"sms-backend","sql":"\n      SELECT \n        s.id,\n        s.student_id,\n        s.first_name,\n        s.last_name,\n        s.middle_name,\n        s.date_of_birth,\n        s.gender,\n        s.phone,\n        s.address,\n        s.admission_date,\n        s.passport_photo,\n        s.status,\n        s.created_at,\n        u.email,\n        c.name as class_name,\n        gl.name as grade_level,\n        ay.name as academic_year,\n        CONCAT(s.first_name, ' ', s.last_name) as full_name\n      FROM students s\n      LEFT JOIN users u ON s.user_id = u.id\n      LEFT JOIN classes c ON s.class_id = c.id\n      LEFT JOIN grade_levels gl ON c.grade_level = gl.name\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      WHERE 1=1\n      ORDER BY s.first_name ASC\n      LIMIT ? OFFSET ?\n    ","sqlMessage":"Unknown column 's.first_name' in 'field list'","sqlState":"42S22","stack":"Error: Unknown column 's.first_name' in 'field list'\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\config\\database.js:48:31)\n    at getStudents (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\studentController.js:1110:28)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-26 04:25:35"}
{"level":"info","message":"::1 - - [26/Jul/2025:15:25:35 +0000] \"GET /api/students?page=1&limit=10&search=&sort_by=first_name&sort_order=ASC HTTP/1.1\" 500 57 \"http://localhost:3001/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-26 04:25:35"}
{"error":"Unknown column 's.first_name' in 'field list'","level":"error","message":"Database query error:","params":[10,0],"query":"\n      SELECT \n        s.id,\n        s.student_id,\n        s.first_name,\n        s.last_name,\n        s.middle_name,\n        s.date_of_birth,\n        s.gender,\n        s.phone,\n        s.address,\n        s.admission_date,\n        s.passport_photo,\n        s.status,\n        s.created_at,\n        u.email,\n        c.name as class_name,\n        gl.name as grade_level,\n        ay.name as academic_year,\n        CONCAT(s.first_name, ' ', s.last_name) as full_name\n      FROM students s\n      LEFT JOIN users u ON s.user_id = u.id\n      LEFT JOIN classes c ON s.class_id = c.id\n      LEFT JOIN grade_levels gl ON c.grade_level = gl.name\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      WHERE 1=1\n      ORDER BY s.first_name ASC\n      LIMIT ? OFFSET ?\n    ","service":"sms-backend","timestamp":"2025-07-26 04:25:35"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Get students error: Unknown column 's.first_name' in 'field list'","service":"sms-backend","sql":"\n      SELECT \n        s.id,\n        s.student_id,\n        s.first_name,\n        s.last_name,\n        s.middle_name,\n        s.date_of_birth,\n        s.gender,\n        s.phone,\n        s.address,\n        s.admission_date,\n        s.passport_photo,\n        s.status,\n        s.created_at,\n        u.email,\n        c.name as class_name,\n        gl.name as grade_level,\n        ay.name as academic_year,\n        CONCAT(s.first_name, ' ', s.last_name) as full_name\n      FROM students s\n      LEFT JOIN users u ON s.user_id = u.id\n      LEFT JOIN classes c ON s.class_id = c.id\n      LEFT JOIN grade_levels gl ON c.grade_level = gl.name\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      WHERE 1=1\n      ORDER BY s.first_name ASC\n      LIMIT ? OFFSET ?\n    ","sqlMessage":"Unknown column 's.first_name' in 'field list'","sqlState":"42S22","stack":"Error: Unknown column 's.first_name' in 'field list'\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\config\\database.js:48:31)\n    at getStudents (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\studentController.js:1110:28)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-26 04:25:35"}
{"level":"info","message":"::1 - - [26/Jul/2025:15:25:35 +0000] \"GET /api/students?page=1&limit=10&search=&sort_by=first_name&sort_order=ASC HTTP/1.1\" 500 57 \"http://localhost:3001/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-26 04:25:35"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-26 04:26:14"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-26 04:26:37"}
{"level":"info","message":"Database connected successfully","service":"sms-backend","timestamp":"2025-07-26 04:26:37"}
{"level":"info","message":"Server running on port 5000 in development mode","service":"sms-backend","timestamp":"2025-07-26 04:26:37"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-26 04:27:04"}
{"level":"info","message":"Database connected successfully","service":"sms-backend","timestamp":"2025-07-26 04:27:04"}
{"level":"info","message":"Server running on port 5000 in development mode","service":"sms-backend","timestamp":"2025-07-26 04:27:04"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-26 04:27:33"}
{"level":"info","message":"Database connected successfully","service":"sms-backend","timestamp":"2025-07-26 04:27:33"}
{"level":"info","message":"Server running on port 5000 in development mode","service":"sms-backend","timestamp":"2025-07-26 04:27:33"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-26 04:27:59"}
{"level":"info","message":"Database connected successfully","service":"sms-backend","timestamp":"2025-07-26 04:27:59"}
{"level":"info","message":"Server running on port 5000 in development mode","service":"sms-backend","timestamp":"2025-07-26 04:27:59"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-26 04:28:14"}
{"level":"info","message":"Database connected successfully","service":"sms-backend","timestamp":"2025-07-26 04:28:14"}
{"level":"info","message":"Server running on port 5000 in development mode","service":"sms-backend","timestamp":"2025-07-26 04:28:14"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-26 04:28:49"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-26 04:29:45"}
{"level":"info","message":"Database connected successfully","service":"sms-backend","timestamp":"2025-07-26 04:29:45"}
{"level":"info","message":"Server running on port 5000 in development mode","service":"sms-backend","timestamp":"2025-07-26 04:29:45"}
{"error":"Table 'school_management_system.admins' doesn't exist","level":"error","message":"Database query error:","params":[2],"query":"SELECT * FROM admins WHERE user_id = ?","service":"sms-backend","timestamp":"2025-07-26 04:30:17"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Get profile error: Table 'school_management_system.admins' doesn't exist","service":"sms-backend","sql":"SELECT * FROM admins WHERE user_id = ?","sqlMessage":"Table 'school_management_system.admins' doesn't exist","sqlState":"42S02","stack":"Error: Table 'school_management_system.admins' doesn't exist\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\config\\database.js:48:31)\n    at getProfile (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\authController.js:256:30)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-26 04:30:17"}
{"level":"info","message":"::1 - - [26/Jul/2025:15:30:17 +0000] \"GET /api/auth/profile HTTP/1.1\" 500 51 \"http://localhost:3001/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-26 04:30:17"}
{"error":"Incorrect arguments to mysqld_stmt_execute","level":"error","message":"Database query error:","params":[10,0],"query":"\n      SELECT\n        s.id,\n        s.student_id,\n        u.first_name,\n        u.last_name,\n        u.date_of_birth,\n        u.gender,\n        u.phone,\n        u.address,\n        s.admission_date,\n        s.admission_number,\n        s.roll_number,\n        s.blood_group,\n        s.nationality,\n        s.religion,\n        s.medical_conditions,\n        s.emergency_contact_name,\n        s.emergency_contact_phone,\n        s.status,\n        s.created_at,\n        u.email,\n        u.profile_picture as passport_photo,\n        c.name as class_name,\n        gl.name as grade_level,\n        ay.name as academic_year,\n        CONCAT(u.first_name, ' ', u.last_name) as full_name\n      FROM students s\n      LEFT JOIN users u ON s.user_id = u.id\n      LEFT JOIN classes c ON s.class_id = c.id\n      LEFT JOIN grade_levels gl ON c.grade_level = gl.name\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      WHERE 1=1\n      ORDER BY u.first_name ASC\n      LIMIT ? OFFSET ?\n    ","service":"sms-backend","timestamp":"2025-07-26 04:30:17"}
{"code":"ER_WRONG_ARGUMENTS","errno":1210,"level":"error","message":"Get students error: Incorrect arguments to mysqld_stmt_execute","service":"sms-backend","sql":"\n      SELECT\n        s.id,\n        s.student_id,\n        u.first_name,\n        u.last_name,\n        u.date_of_birth,\n        u.gender,\n        u.phone,\n        u.address,\n        s.admission_date,\n        s.admission_number,\n        s.roll_number,\n        s.blood_group,\n        s.nationality,\n        s.religion,\n        s.medical_conditions,\n        s.emergency_contact_name,\n        s.emergency_contact_phone,\n        s.status,\n        s.created_at,\n        u.email,\n        u.profile_picture as passport_photo,\n        c.name as class_name,\n        gl.name as grade_level,\n        ay.name as academic_year,\n        CONCAT(u.first_name, ' ', u.last_name) as full_name\n      FROM students s\n      LEFT JOIN users u ON s.user_id = u.id\n      LEFT JOIN classes c ON s.class_id = c.id\n      LEFT JOIN grade_levels gl ON c.grade_level = gl.name\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      WHERE 1=1\n      ORDER BY u.first_name ASC\n      LIMIT ? OFFSET ?\n    ","sqlMessage":"Incorrect arguments to mysqld_stmt_execute","sqlState":"HY000","stack":"Error: Incorrect arguments to mysqld_stmt_execute\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\config\\database.js:48:31)\n    at getStudents (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\studentController.js:1127:28)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-26 04:30:17"}
{"level":"info","message":"::1 - - [26/Jul/2025:15:30:17 +0000] \"GET /api/students?page=1&limit=10&search=&sort_by=first_name&sort_order=ASC HTTP/1.1\" 500 57 \"http://localhost:3001/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-26 04:30:17"}
{"error":"Incorrect arguments to mysqld_stmt_execute","level":"error","message":"Database query error:","params":[10,0],"query":"\n      SELECT\n        s.id,\n        s.student_id,\n        u.first_name,\n        u.last_name,\n        u.date_of_birth,\n        u.gender,\n        u.phone,\n        u.address,\n        s.admission_date,\n        s.admission_number,\n        s.roll_number,\n        s.blood_group,\n        s.nationality,\n        s.religion,\n        s.medical_conditions,\n        s.emergency_contact_name,\n        s.emergency_contact_phone,\n        s.status,\n        s.created_at,\n        u.email,\n        u.profile_picture as passport_photo,\n        c.name as class_name,\n        gl.name as grade_level,\n        ay.name as academic_year,\n        CONCAT(u.first_name, ' ', u.last_name) as full_name\n      FROM students s\n      LEFT JOIN users u ON s.user_id = u.id\n      LEFT JOIN classes c ON s.class_id = c.id\n      LEFT JOIN grade_levels gl ON c.grade_level = gl.name\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      WHERE 1=1\n      ORDER BY u.first_name ASC\n      LIMIT ? OFFSET ?\n    ","service":"sms-backend","timestamp":"2025-07-26 04:30:17"}
{"code":"ER_WRONG_ARGUMENTS","errno":1210,"level":"error","message":"Get students error: Incorrect arguments to mysqld_stmt_execute","service":"sms-backend","sql":"\n      SELECT\n        s.id,\n        s.student_id,\n        u.first_name,\n        u.last_name,\n        u.date_of_birth,\n        u.gender,\n        u.phone,\n        u.address,\n        s.admission_date,\n        s.admission_number,\n        s.roll_number,\n        s.blood_group,\n        s.nationality,\n        s.religion,\n        s.medical_conditions,\n        s.emergency_contact_name,\n        s.emergency_contact_phone,\n        s.status,\n        s.created_at,\n        u.email,\n        u.profile_picture as passport_photo,\n        c.name as class_name,\n        gl.name as grade_level,\n        ay.name as academic_year,\n        CONCAT(u.first_name, ' ', u.last_name) as full_name\n      FROM students s\n      LEFT JOIN users u ON s.user_id = u.id\n      LEFT JOIN classes c ON s.class_id = c.id\n      LEFT JOIN grade_levels gl ON c.grade_level = gl.name\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      WHERE 1=1\n      ORDER BY u.first_name ASC\n      LIMIT ? OFFSET ?\n    ","sqlMessage":"Incorrect arguments to mysqld_stmt_execute","sqlState":"HY000","stack":"Error: Incorrect arguments to mysqld_stmt_execute\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\config\\database.js:48:31)\n    at getStudents (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\studentController.js:1127:28)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-26 04:30:17"}
{"level":"info","message":"::1 - - [26/Jul/2025:15:30:17 +0000] \"GET /api/students?page=1&limit=10&search=&sort_by=first_name&sort_order=ASC HTTP/1.1\" 500 57 \"http://localhost:3001/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-26 04:30:17"}
{"error":"Table 'school_management_system.admins' doesn't exist","level":"error","message":"Database query error:","params":[2],"query":"SELECT * FROM admins WHERE user_id = ?","service":"sms-backend","timestamp":"2025-07-26 04:30:23"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Get profile error: Table 'school_management_system.admins' doesn't exist","service":"sms-backend","sql":"SELECT * FROM admins WHERE user_id = ?","sqlMessage":"Table 'school_management_system.admins' doesn't exist","sqlState":"42S02","stack":"Error: Table 'school_management_system.admins' doesn't exist\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\config\\database.js:48:31)\n    at getProfile (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\authController.js:256:30)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-26 04:30:23"}
{"level":"info","message":"::1 - - [26/Jul/2025:15:30:23 +0000] \"GET /api/auth/profile HTTP/1.1\" 500 51 \"http://localhost:3001/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-26 04:30:23"}
{"error":"Incorrect arguments to mysqld_stmt_execute","level":"error","message":"Database query error:","params":[10,0],"query":"\n      SELECT\n        s.id,\n        s.student_id,\n        u.first_name,\n        u.last_name,\n        u.date_of_birth,\n        u.gender,\n        u.phone,\n        u.address,\n        s.admission_date,\n        s.admission_number,\n        s.roll_number,\n        s.blood_group,\n        s.nationality,\n        s.religion,\n        s.medical_conditions,\n        s.emergency_contact_name,\n        s.emergency_contact_phone,\n        s.status,\n        s.created_at,\n        u.email,\n        u.profile_picture as passport_photo,\n        c.name as class_name,\n        gl.name as grade_level,\n        ay.name as academic_year,\n        CONCAT(u.first_name, ' ', u.last_name) as full_name\n      FROM students s\n      LEFT JOIN users u ON s.user_id = u.id\n      LEFT JOIN classes c ON s.class_id = c.id\n      LEFT JOIN grade_levels gl ON c.grade_level = gl.name\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      WHERE 1=1\n      ORDER BY u.first_name ASC\n      LIMIT ? OFFSET ?\n    ","service":"sms-backend","timestamp":"2025-07-26 04:30:23"}
{"code":"ER_WRONG_ARGUMENTS","errno":1210,"level":"error","message":"Get students error: Incorrect arguments to mysqld_stmt_execute","service":"sms-backend","sql":"\n      SELECT\n        s.id,\n        s.student_id,\n        u.first_name,\n        u.last_name,\n        u.date_of_birth,\n        u.gender,\n        u.phone,\n        u.address,\n        s.admission_date,\n        s.admission_number,\n        s.roll_number,\n        s.blood_group,\n        s.nationality,\n        s.religion,\n        s.medical_conditions,\n        s.emergency_contact_name,\n        s.emergency_contact_phone,\n        s.status,\n        s.created_at,\n        u.email,\n        u.profile_picture as passport_photo,\n        c.name as class_name,\n        gl.name as grade_level,\n        ay.name as academic_year,\n        CONCAT(u.first_name, ' ', u.last_name) as full_name\n      FROM students s\n      LEFT JOIN users u ON s.user_id = u.id\n      LEFT JOIN classes c ON s.class_id = c.id\n      LEFT JOIN grade_levels gl ON c.grade_level = gl.name\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      WHERE 1=1\n      ORDER BY u.first_name ASC\n      LIMIT ? OFFSET ?\n    ","sqlMessage":"Incorrect arguments to mysqld_stmt_execute","sqlState":"HY000","stack":"Error: Incorrect arguments to mysqld_stmt_execute\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\config\\database.js:48:31)\n    at getStudents (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\studentController.js:1127:28)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-26 04:30:23"}
{"level":"info","message":"::1 - - [26/Jul/2025:15:30:23 +0000] \"GET /api/students?page=1&limit=10&search=&sort_by=first_name&sort_order=ASC HTTP/1.1\" 500 57 \"http://localhost:3001/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-26 04:30:23"}
{"error":"Incorrect arguments to mysqld_stmt_execute","level":"error","message":"Database query error:","params":[10,0],"query":"\n      SELECT\n        s.id,\n        s.student_id,\n        u.first_name,\n        u.last_name,\n        u.date_of_birth,\n        u.gender,\n        u.phone,\n        u.address,\n        s.admission_date,\n        s.admission_number,\n        s.roll_number,\n        s.blood_group,\n        s.nationality,\n        s.religion,\n        s.medical_conditions,\n        s.emergency_contact_name,\n        s.emergency_contact_phone,\n        s.status,\n        s.created_at,\n        u.email,\n        u.profile_picture as passport_photo,\n        c.name as class_name,\n        gl.name as grade_level,\n        ay.name as academic_year,\n        CONCAT(u.first_name, ' ', u.last_name) as full_name\n      FROM students s\n      LEFT JOIN users u ON s.user_id = u.id\n      LEFT JOIN classes c ON s.class_id = c.id\n      LEFT JOIN grade_levels gl ON c.grade_level = gl.name\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      WHERE 1=1\n      ORDER BY u.first_name ASC\n      LIMIT ? OFFSET ?\n    ","service":"sms-backend","timestamp":"2025-07-26 04:30:23"}
{"code":"ER_WRONG_ARGUMENTS","errno":1210,"level":"error","message":"Get students error: Incorrect arguments to mysqld_stmt_execute","service":"sms-backend","sql":"\n      SELECT\n        s.id,\n        s.student_id,\n        u.first_name,\n        u.last_name,\n        u.date_of_birth,\n        u.gender,\n        u.phone,\n        u.address,\n        s.admission_date,\n        s.admission_number,\n        s.roll_number,\n        s.blood_group,\n        s.nationality,\n        s.religion,\n        s.medical_conditions,\n        s.emergency_contact_name,\n        s.emergency_contact_phone,\n        s.status,\n        s.created_at,\n        u.email,\n        u.profile_picture as passport_photo,\n        c.name as class_name,\n        gl.name as grade_level,\n        ay.name as academic_year,\n        CONCAT(u.first_name, ' ', u.last_name) as full_name\n      FROM students s\n      LEFT JOIN users u ON s.user_id = u.id\n      LEFT JOIN classes c ON s.class_id = c.id\n      LEFT JOIN grade_levels gl ON c.grade_level = gl.name\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      WHERE 1=1\n      ORDER BY u.first_name ASC\n      LIMIT ? OFFSET ?\n    ","sqlMessage":"Incorrect arguments to mysqld_stmt_execute","sqlState":"HY000","stack":"Error: Incorrect arguments to mysqld_stmt_execute\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\config\\database.js:48:31)\n    at getStudents (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\studentController.js:1127:28)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-26 04:30:23"}
{"level":"info","message":"::1 - - [26/Jul/2025:15:30:23 +0000] \"GET /api/students?page=1&limit=10&search=&sort_by=first_name&sort_order=ASC HTTP/1.1\" 500 57 \"http://localhost:3001/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-26 04:30:23"}
{"level":"info","message":"User <EMAIL> logged in successfully","service":"sms-backend","timestamp":"2025-07-26 04:30:30"}
{"level":"info","message":"::1 - - [26/Jul/2025:15:30:30 +0000] \"POST /api/auth/login HTTP/1.1\" 200 846 \"-\" \"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.19041.6093\"","service":"sms-backend","timestamp":"2025-07-26 04:30:30"}
{"error":"Incorrect arguments to mysqld_stmt_execute","level":"error","message":"Database query error:","params":[10,0],"query":"\n      SELECT\n        s.id,\n        s.student_id,\n        u.first_name,\n        u.last_name,\n        u.date_of_birth,\n        u.gender,\n        u.phone,\n        u.address,\n        s.admission_date,\n        s.admission_number,\n        s.roll_number,\n        s.blood_group,\n        s.nationality,\n        s.religion,\n        s.medical_conditions,\n        s.emergency_contact_name,\n        s.emergency_contact_phone,\n        s.status,\n        s.created_at,\n        u.email,\n        u.profile_picture as passport_photo,\n        c.name as class_name,\n        gl.name as grade_level,\n        ay.name as academic_year,\n        CONCAT(u.first_name, ' ', u.last_name) as full_name\n      FROM students s\n      LEFT JOIN users u ON s.user_id = u.id\n      LEFT JOIN classes c ON s.class_id = c.id\n      LEFT JOIN grade_levels gl ON c.grade_level = gl.name\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      WHERE 1=1\n      ORDER BY u.first_name ASC\n      LIMIT ? OFFSET ?\n    ","service":"sms-backend","timestamp":"2025-07-26 04:30:30"}
{"code":"ER_WRONG_ARGUMENTS","errno":1210,"level":"error","message":"Get students error: Incorrect arguments to mysqld_stmt_execute","service":"sms-backend","sql":"\n      SELECT\n        s.id,\n        s.student_id,\n        u.first_name,\n        u.last_name,\n        u.date_of_birth,\n        u.gender,\n        u.phone,\n        u.address,\n        s.admission_date,\n        s.admission_number,\n        s.roll_number,\n        s.blood_group,\n        s.nationality,\n        s.religion,\n        s.medical_conditions,\n        s.emergency_contact_name,\n        s.emergency_contact_phone,\n        s.status,\n        s.created_at,\n        u.email,\n        u.profile_picture as passport_photo,\n        c.name as class_name,\n        gl.name as grade_level,\n        ay.name as academic_year,\n        CONCAT(u.first_name, ' ', u.last_name) as full_name\n      FROM students s\n      LEFT JOIN users u ON s.user_id = u.id\n      LEFT JOIN classes c ON s.class_id = c.id\n      LEFT JOIN grade_levels gl ON c.grade_level = gl.name\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      WHERE 1=1\n      ORDER BY u.first_name ASC\n      LIMIT ? OFFSET ?\n    ","sqlMessage":"Incorrect arguments to mysqld_stmt_execute","sqlState":"HY000","stack":"Error: Incorrect arguments to mysqld_stmt_execute\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\config\\database.js:48:31)\n    at getStudents (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\studentController.js:1127:28)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-26 04:30:30"}
{"level":"info","message":"::1 - - [26/Jul/2025:15:30:30 +0000] \"GET /api/students?page=1&limit=10&search=&sort_by=first_name&sort_order=ASC HTTP/1.1\" 500 57 \"-\" \"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.19041.6093\"","service":"sms-backend","timestamp":"2025-07-26 04:30:30"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-26 04:31:10"}
{"level":"info","message":"Database connected successfully","service":"sms-backend","timestamp":"2025-07-26 04:31:10"}
{"level":"info","message":"Server running on port 5000 in development mode","service":"sms-backend","timestamp":"2025-07-26 04:31:10"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-26 04:31:10"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-26 04:31:17"}
{"error":"Incorrect arguments to mysqld_stmt_execute","level":"error","message":"Database query error:","params":[10,0],"query":"\n      SELECT \n        s.id,\n        s.student_id,\n        u.first_name,\n        u.last_name,\n        u.email\n      FROM students s\n      LEFT JOIN users u ON s.user_id = u.id\n      WHERE 1=1\n      ORDER BY u.first_name ASC\n      LIMIT ? OFFSET ?\n    ","service":"sms-backend","timestamp":"2025-07-26 04:31:17"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-26 04:31:34"}
{"level":"info","message":"Database connected successfully","service":"sms-backend","timestamp":"2025-07-26 04:31:34"}
{"level":"info","message":"Server running on port 5000 in development mode","service":"sms-backend","timestamp":"2025-07-26 04:31:34"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-26 04:31:34"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-26 04:31:46"}
{"error":"Incorrect arguments to mysqld_stmt_execute","level":"error","message":"Database query error:","params":[0,10],"query":"\n      SELECT\n        s.id,\n        s.student_id,\n        u.first_name,\n        u.last_name,\n        u.email\n      FROM students s\n      LEFT JOIN users u ON s.user_id = u.id\n      WHERE 1=1\n      ORDER BY u.first_name ASC\n      LIMIT ?, ?\n    ","service":"sms-backend","timestamp":"2025-07-26 04:31:46"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-26 04:31:59"}
{"level":"info","message":"Database connected successfully","service":"sms-backend","timestamp":"2025-07-26 04:31:59"}
{"level":"info","message":"Server running on port 5000 in development mode","service":"sms-backend","timestamp":"2025-07-26 04:31:59"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-26 04:31:59"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-26 04:32:10"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-26 04:32:55"}
{"level":"info","message":"Database connected successfully","service":"sms-backend","timestamp":"2025-07-26 04:32:55"}
{"level":"info","message":"Server running on port 5000 in development mode","service":"sms-backend","timestamp":"2025-07-26 04:32:55"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-26 04:33:57"}
{"level":"info","message":"Database connected successfully","service":"sms-backend","timestamp":"2025-07-26 04:33:57"}
{"level":"info","message":"Server running on port 5000 in development mode","service":"sms-backend","timestamp":"2025-07-26 04:33:57"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-26 04:33:57"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-26 04:35:11"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-26 04:35:31"}
{"level":"info","message":"Database connected successfully","service":"sms-backend","timestamp":"2025-07-26 04:35:31"}
{"level":"info","message":"Server running on port 5000 in development mode","service":"sms-backend","timestamp":"2025-07-26 04:35:31"}
{"level":"info","message":"User <EMAIL> logged in successfully","service":"sms-backend","timestamp":"2025-07-26 04:35:47"}
{"level":"info","message":"::1 - - [26/Jul/2025:15:35:47 +0000] \"POST /api/auth/login HTTP/1.1\" 200 846 \"-\" \"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.19041.6093\"","service":"sms-backend","timestamp":"2025-07-26 04:35:47"}
{"level":"info","message":"::1 - - [26/Jul/2025:15:35:47 +0000] \"GET /api/students?page=1&limit=10&search=&sort_by=first_name&sort_order=ASC HTTP/1.1\" 200 158 \"-\" \"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.19041.6093\"","service":"sms-backend","timestamp":"2025-07-26 04:35:47"}
{"error":"Table 'school_management_system.admins' doesn't exist","level":"error","message":"Database query error:","params":[2],"query":"SELECT * FROM admins WHERE user_id = ?","service":"sms-backend","timestamp":"2025-07-26 04:38:18"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Get profile error: Table 'school_management_system.admins' doesn't exist","service":"sms-backend","sql":"SELECT * FROM admins WHERE user_id = ?","sqlMessage":"Table 'school_management_system.admins' doesn't exist","sqlState":"42S02","stack":"Error: Table 'school_management_system.admins' doesn't exist\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\config\\database.js:48:31)\n    at getProfile (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\authController.js:256:30)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-26 04:38:18"}
{"level":"info","message":"::1 - - [26/Jul/2025:15:38:18 +0000] \"GET /api/auth/profile HTTP/1.1\" 500 51 \"http://localhost:3001/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-26 04:38:18"}
{"level":"info","message":"::1 - - [26/Jul/2025:15:38:18 +0000] \"GET /api/students?page=1&limit=10&search=&sort_by=first_name&sort_order=ASC HTTP/1.1\" 200 158 \"http://localhost:3001/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-26 04:38:18"}
{"level":"info","message":"::1 - - [26/Jul/2025:15:38:18 +0000] \"GET /api/students?page=1&limit=10&search=&sort_by=first_name&sort_order=ASC HTTP/1.1\" 304 - \"http://localhost:3001/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-26 04:38:18"}
{"error":"Table 'school_management_system.admins' doesn't exist","level":"error","message":"Database query error:","params":[2],"query":"SELECT * FROM admins WHERE user_id = ?","service":"sms-backend","timestamp":"2025-07-26 04:38:26"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Get profile error: Table 'school_management_system.admins' doesn't exist","service":"sms-backend","sql":"SELECT * FROM admins WHERE user_id = ?","sqlMessage":"Table 'school_management_system.admins' doesn't exist","sqlState":"42S02","stack":"Error: Table 'school_management_system.admins' doesn't exist\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\config\\database.js:48:31)\n    at getProfile (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\authController.js:256:30)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-26 04:38:26"}
{"level":"info","message":"::1 - - [26/Jul/2025:15:38:26 +0000] \"GET /api/auth/profile HTTP/1.1\" 500 51 \"http://localhost:3001/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-26 04:38:26"}
{"level":"info","message":"::1 - - [26/Jul/2025:15:38:26 +0000] \"GET /api/students?page=1&limit=10&search=&sort_by=first_name&sort_order=ASC HTTP/1.1\" 304 - \"http://localhost:3001/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-26 04:38:26"}
{"level":"info","message":"::1 - - [26/Jul/2025:15:38:26 +0000] \"GET /api/students?page=1&limit=10&search=&sort_by=first_name&sort_order=ASC HTTP/1.1\" 304 - \"http://localhost:3001/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-26 04:38:26"}
{"error":"Unknown column 'c.grade_level_id' in 'on clause'","level":"error","message":"Database query error:","params":[],"query":"\n      SELECT COUNT(*) as total\n      FROM classes c\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      LEFT JOIN teachers t ON c.class_teacher_id = t.id\n      WHERE 1=1\n    ","service":"sms-backend","timestamp":"2025-07-26 04:49:51"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Get classes error: Unknown column 'c.grade_level_id' in 'on clause'","service":"sms-backend","sql":"\n      SELECT COUNT(*) as total\n      FROM classes c\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      LEFT JOIN teachers t ON c.class_teacher_id = t.id\n      WHERE 1=1\n    ","sqlMessage":"Unknown column 'c.grade_level_id' in 'on clause'","sqlState":"42S22","stack":"Error: Unknown column 'c.grade_level_id' in 'on clause'\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\config\\database.js:48:31)\n    at getClasses (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\classController.js:65:31)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at handleValidationErrors (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\utils\\validation.js:21:3)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\route.js:149:13)","timestamp":"2025-07-26 04:49:51"}
{"level":"info","message":"::1 - - [26/Jul/2025:15:49:51 +0000] \"GET /api/classes HTTP/1.1\" 500 56 \"http://localhost:3001/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-26 04:49:51"}
{"error":"Table 'school_management_system.admins' doesn't exist","level":"error","message":"Database query error:","params":[2],"query":"SELECT * FROM admins WHERE user_id = ?","service":"sms-backend","timestamp":"2025-07-26 04:50:53"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Get profile error: Table 'school_management_system.admins' doesn't exist","service":"sms-backend","sql":"SELECT * FROM admins WHERE user_id = ?","sqlMessage":"Table 'school_management_system.admins' doesn't exist","sqlState":"42S02","stack":"Error: Table 'school_management_system.admins' doesn't exist\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\config\\database.js:48:31)\n    at getProfile (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\authController.js:256:30)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-26 04:50:53"}
{"level":"info","message":"::1 - - [26/Jul/2025:15:50:53 +0000] \"GET /api/auth/profile HTTP/1.1\" 500 51 \"http://localhost:3001/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-26 04:50:53"}
{"level":"info","message":"::1 - - [26/Jul/2025:15:50:53 +0000] \"GET /api/students?page=1&limit=10&search=&sort_by=first_name&sort_order=ASC HTTP/1.1\" 304 - \"http://localhost:3001/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-26 04:50:53"}
{"level":"info","message":"::1 - - [26/Jul/2025:15:50:53 +0000] \"GET /api/students?page=1&limit=10&search=&sort_by=first_name&sort_order=ASC HTTP/1.1\" 304 - \"http://localhost:3001/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-26 04:50:53"}
{"error":"Table 'school_management_system.admins' doesn't exist","level":"error","message":"Database query error:","params":[2],"query":"SELECT * FROM admins WHERE user_id = ?","service":"sms-backend","timestamp":"2025-07-26 04:51:17"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Get profile error: Table 'school_management_system.admins' doesn't exist","service":"sms-backend","sql":"SELECT * FROM admins WHERE user_id = ?","sqlMessage":"Table 'school_management_system.admins' doesn't exist","sqlState":"42S02","stack":"Error: Table 'school_management_system.admins' doesn't exist\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\config\\database.js:48:31)\n    at getProfile (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\authController.js:256:30)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-26 04:51:17"}
{"level":"info","message":"::1 - - [26/Jul/2025:15:51:17 +0000] \"GET /api/auth/profile HTTP/1.1\" 500 51 \"http://localhost:3001/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-26 04:51:17"}
{"level":"info","message":"::1 - - [26/Jul/2025:15:51:17 +0000] \"GET /api/students?page=1&limit=10&search=&sort_by=first_name&sort_order=ASC HTTP/1.1\" 304 - \"http://localhost:3001/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-26 04:51:17"}
{"level":"info","message":"::1 - - [26/Jul/2025:15:51:17 +0000] \"GET /api/students?page=1&limit=10&search=&sort_by=first_name&sort_order=ASC HTTP/1.1\" 304 - \"http://localhost:3001/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-26 04:51:17"}
{"error":"Table 'school_management_system.admins' doesn't exist","level":"error","message":"Database query error:","params":[2],"query":"SELECT * FROM admins WHERE user_id = ?","service":"sms-backend","timestamp":"2025-07-26 04:51:49"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Get profile error: Table 'school_management_system.admins' doesn't exist","service":"sms-backend","sql":"SELECT * FROM admins WHERE user_id = ?","sqlMessage":"Table 'school_management_system.admins' doesn't exist","sqlState":"42S02","stack":"Error: Table 'school_management_system.admins' doesn't exist\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\config\\database.js:48:31)\n    at getProfile (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\authController.js:256:30)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-26 04:51:49"}
{"level":"info","message":"::1 - - [26/Jul/2025:15:51:49 +0000] \"GET /api/auth/profile HTTP/1.1\" 500 51 \"http://localhost:3001/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-26 04:51:49"}
{"level":"info","message":"::1 - - [26/Jul/2025:15:51:49 +0000] \"GET /api/students?page=1&limit=10&search=&sort_by=first_name&sort_order=ASC HTTP/1.1\" 304 - \"http://localhost:3001/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-26 04:51:49"}
{"level":"info","message":"::1 - - [26/Jul/2025:15:51:49 +0000] \"GET /api/students?page=1&limit=10&search=&sort_by=first_name&sort_order=ASC HTTP/1.1\" 304 - \"http://localhost:3001/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-26 04:51:49"}
{"error":"Unknown column 'c.grade_level_id' in 'on clause'","level":"error","message":"Database query error:","params":[],"query":"\n      SELECT COUNT(*) as total\n      FROM classes c\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      LEFT JOIN teachers t ON c.class_teacher_id = t.id\n      WHERE 1=1\n    ","service":"sms-backend","timestamp":"2025-07-26 04:53:30"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Get classes error: Unknown column 'c.grade_level_id' in 'on clause'","service":"sms-backend","sql":"\n      SELECT COUNT(*) as total\n      FROM classes c\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      LEFT JOIN teachers t ON c.class_teacher_id = t.id\n      WHERE 1=1\n    ","sqlMessage":"Unknown column 'c.grade_level_id' in 'on clause'","sqlState":"42S22","stack":"Error: Unknown column 'c.grade_level_id' in 'on clause'\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\config\\database.js:48:31)\n    at getClasses (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\classController.js:65:31)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at handleValidationErrors (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\utils\\validation.js:21:3)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\route.js:149:13)","timestamp":"2025-07-26 04:53:30"}
{"level":"info","message":"::1 - - [26/Jul/2025:15:53:30 +0000] \"GET /api/classes HTTP/1.1\" 500 56 \"http://localhost:3001/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-26 04:53:30"}
{"error":"Unknown column 'c.grade_level_id' in 'on clause'","level":"error","message":"Database query error:","params":[],"query":"\n      SELECT COUNT(*) as total\n      FROM classes c\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      LEFT JOIN teachers t ON c.class_teacher_id = t.id\n      WHERE 1=1\n    ","service":"sms-backend","timestamp":"2025-07-26 04:53:39"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Get classes error: Unknown column 'c.grade_level_id' in 'on clause'","service":"sms-backend","sql":"\n      SELECT COUNT(*) as total\n      FROM classes c\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      LEFT JOIN teachers t ON c.class_teacher_id = t.id\n      WHERE 1=1\n    ","sqlMessage":"Unknown column 'c.grade_level_id' in 'on clause'","sqlState":"42S22","stack":"Error: Unknown column 'c.grade_level_id' in 'on clause'\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\config\\database.js:48:31)\n    at getClasses (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\classController.js:65:31)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at handleValidationErrors (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\utils\\validation.js:21:3)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\route.js:149:13)","timestamp":"2025-07-26 04:53:39"}
{"level":"info","message":"::1 - - [26/Jul/2025:15:53:39 +0000] \"GET /api/classes HTTP/1.1\" 500 56 \"http://localhost:3001/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-26 04:53:39"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-26 04:53:49"}
{"level":"info","message":"Database connected successfully","service":"sms-backend","timestamp":"2025-07-26 04:53:49"}
{"level":"info","message":"Server running on port 5000 in development mode","service":"sms-backend","timestamp":"2025-07-26 04:53:49"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-26 04:54:04"}
{"level":"info","message":"Database connected successfully","service":"sms-backend","timestamp":"2025-07-26 04:54:04"}
{"level":"info","message":"Server running on port 5000 in development mode","service":"sms-backend","timestamp":"2025-07-26 04:54:04"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-26 04:54:22"}
{"level":"info","message":"Database connected successfully","service":"sms-backend","timestamp":"2025-07-26 04:54:22"}
{"level":"info","message":"Server running on port 5000 in development mode","service":"sms-backend","timestamp":"2025-07-26 04:54:22"}
{"error":"Table 'school_management_system.admins' doesn't exist","level":"error","message":"Database query error:","params":[2],"query":"SELECT * FROM admins WHERE user_id = ?","service":"sms-backend","timestamp":"2025-07-26 04:54:26"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Get profile error: Table 'school_management_system.admins' doesn't exist","service":"sms-backend","sql":"SELECT * FROM admins WHERE user_id = ?","sqlMessage":"Table 'school_management_system.admins' doesn't exist","sqlState":"42S02","stack":"Error: Table 'school_management_system.admins' doesn't exist\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\config\\database.js:48:31)\n    at getProfile (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\authController.js:256:30)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-26 04:54:26"}
{"level":"info","message":"::1 - - [26/Jul/2025:15:54:26 +0000] \"GET /api/auth/profile HTTP/1.1\" 500 51 \"http://localhost:3001/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-26 04:54:26"}
{"error":"Unknown column 'c.grade_level_id' in 'on clause'","level":"error","message":"Database query error:","params":[],"query":"\n      SELECT COUNT(*) as total\n      FROM classes c\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      LEFT JOIN teachers t ON c.class_teacher_id = t.id\n      WHERE 1=1\n    ","service":"sms-backend","timestamp":"2025-07-26 04:54:26"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Get classes error: Unknown column 'c.grade_level_id' in 'on clause'","service":"sms-backend","sql":"\n      SELECT COUNT(*) as total\n      FROM classes c\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      LEFT JOIN teachers t ON c.class_teacher_id = t.id\n      WHERE 1=1\n    ","sqlMessage":"Unknown column 'c.grade_level_id' in 'on clause'","sqlState":"42S22","stack":"Error: Unknown column 'c.grade_level_id' in 'on clause'\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\config\\database.js:48:31)\n    at getClasses (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\classController.js:65:31)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at handleValidationErrors (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\utils\\validation.js:21:3)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\route.js:149:13)","timestamp":"2025-07-26 04:54:26"}
{"level":"info","message":"::1 - - [26/Jul/2025:15:54:26 +0000] \"GET /api/classes?page=1&limit=10&search=&sort_by=name&sort_order=ASC HTTP/1.1\" 500 56 \"http://localhost:3001/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-26 04:54:26"}
{"error":"Unknown column 'c.grade_level_id' in 'on clause'","level":"error","message":"Database query error:","params":[],"query":"\n      SELECT COUNT(*) as total\n      FROM classes c\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      LEFT JOIN teachers t ON c.class_teacher_id = t.id\n      WHERE 1=1\n    ","service":"sms-backend","timestamp":"2025-07-26 04:54:26"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Get classes error: Unknown column 'c.grade_level_id' in 'on clause'","service":"sms-backend","sql":"\n      SELECT COUNT(*) as total\n      FROM classes c\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      LEFT JOIN teachers t ON c.class_teacher_id = t.id\n      WHERE 1=1\n    ","sqlMessage":"Unknown column 'c.grade_level_id' in 'on clause'","sqlState":"42S22","stack":"Error: Unknown column 'c.grade_level_id' in 'on clause'\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\config\\database.js:48:31)\n    at getClasses (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\classController.js:65:31)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at handleValidationErrors (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\utils\\validation.js:21:3)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\route.js:149:13)","timestamp":"2025-07-26 04:54:26"}
{"level":"info","message":"::1 - - [26/Jul/2025:15:54:26 +0000] \"GET /api/classes?page=1&limit=10&search=&sort_by=name&sort_order=ASC HTTP/1.1\" 500 56 \"http://localhost:3001/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-26 04:54:26"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-26 04:54:35"}
{"level":"info","message":"Database connected successfully","service":"sms-backend","timestamp":"2025-07-26 04:54:35"}
{"level":"info","message":"Server running on port 5000 in development mode","service":"sms-backend","timestamp":"2025-07-26 04:54:35"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-26 04:54:57"}
{"level":"info","message":"Database connected successfully","service":"sms-backend","timestamp":"2025-07-26 04:54:57"}
{"level":"info","message":"Server running on port 5000 in development mode","service":"sms-backend","timestamp":"2025-07-26 04:54:57"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-26 04:55:12"}
{"level":"info","message":"Database connected successfully","service":"sms-backend","timestamp":"2025-07-26 04:55:12"}
{"level":"info","message":"Server running on port 5000 in development mode","service":"sms-backend","timestamp":"2025-07-26 04:55:12"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-26 04:55:27"}
{"level":"info","message":"Database connected successfully","service":"sms-backend","timestamp":"2025-07-26 04:55:27"}
{"level":"info","message":"Server running on port 5000 in development mode","service":"sms-backend","timestamp":"2025-07-26 04:55:27"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-26 04:56:08"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-26 04:57:28"}
{"level":"info","message":"Database connected successfully","service":"sms-backend","timestamp":"2025-07-26 04:57:28"}
{"level":"info","message":"Server running on port 5000 in development mode","service":"sms-backend","timestamp":"2025-07-26 04:57:28"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-26 04:57:28"}
{"error":"Table 'school_management_system.admins' doesn't exist","level":"error","message":"Database query error:","params":[2],"query":"SELECT * FROM admins WHERE user_id = ?","service":"sms-backend","timestamp":"2025-07-26 04:57:48"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Get profile error: Table 'school_management_system.admins' doesn't exist","service":"sms-backend","sql":"SELECT * FROM admins WHERE user_id = ?","sqlMessage":"Table 'school_management_system.admins' doesn't exist","sqlState":"42S02","stack":"Error: Table 'school_management_system.admins' doesn't exist\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\config\\database.js:48:31)\n    at getProfile (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\authController.js:256:30)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-26 04:57:48"}
{"level":"info","message":"::1 - - [26/Jul/2025:15:57:48 +0000] \"GET /api/auth/profile HTTP/1.1\" 500 51 \"http://localhost:3001/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-26 04:57:48"}
{"level":"info","message":"::1 - - [26/Jul/2025:15:57:48 +0000] \"GET /api/students?page=1&limit=10&search=&sort_by=first_name&sort_order=ASC HTTP/1.1\" 304 - \"http://localhost:3001/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-26 04:57:48"}
{"level":"info","message":"::1 - - [26/Jul/2025:15:57:48 +0000] \"GET /api/students?page=1&limit=10&search=&sort_by=first_name&sort_order=ASC HTTP/1.1\" 304 - \"http://localhost:3001/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-26 04:57:48"}
{"error":"Unknown column 'c.grade_level_id' in 'on clause'","level":"error","message":"Database query error:","params":[],"query":"\n      SELECT COUNT(*) as total\n      FROM classes c\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      LEFT JOIN teachers t ON c.class_teacher_id = t.id\n      WHERE 1=1\n    ","service":"sms-backend","timestamp":"2025-07-26 05:07:40"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Get classes error: Unknown column 'c.grade_level_id' in 'on clause'","service":"sms-backend","sql":"\n      SELECT COUNT(*) as total\n      FROM classes c\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      LEFT JOIN teachers t ON c.class_teacher_id = t.id\n      WHERE 1=1\n    ","sqlMessage":"Unknown column 'c.grade_level_id' in 'on clause'","sqlState":"42S22","stack":"Error: Unknown column 'c.grade_level_id' in 'on clause'\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\config\\database.js:48:31)\n    at getClasses (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\classController.js:65:31)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at handleValidationErrors (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\utils\\validation.js:21:3)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\route.js:149:13)","timestamp":"2025-07-26 05:07:40"}
{"level":"info","message":"::1 - - [26/Jul/2025:16:07:40 +0000] \"GET /api/classes HTTP/1.1\" 500 56 \"http://localhost:3001/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-26 05:07:40"}
{"error":"Table 'school_management_system.admins' doesn't exist","level":"error","message":"Database query error:","params":[2],"query":"SELECT * FROM admins WHERE user_id = ?","service":"sms-backend","timestamp":"2025-07-26 05:12:48"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Get profile error: Table 'school_management_system.admins' doesn't exist","service":"sms-backend","sql":"SELECT * FROM admins WHERE user_id = ?","sqlMessage":"Table 'school_management_system.admins' doesn't exist","sqlState":"42S02","stack":"Error: Table 'school_management_system.admins' doesn't exist\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\config\\database.js:48:31)\n    at getProfile (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\authController.js:256:30)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-26 05:12:48"}
{"level":"info","message":"::1 - - [26/Jul/2025:16:12:48 +0000] \"GET /api/auth/profile HTTP/1.1\" 500 51 \"http://localhost:3001/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-26 05:12:48"}
{"error":"Unknown column 'date' in 'where clause'","level":"error","message":"Database query error:","params":["2025-07-26"],"query":"\n        SELECT\n          COUNT(*) as total_records,\n          SUM(CASE WHEN status = 'present' THEN 1 ELSE 0 END) as present_count,\n          SUM(CASE WHEN status = 'absent' THEN 1 ELSE 0 END) as absent_count,\n          SUM(CASE WHEN status = 'late' THEN 1 ELSE 0 END) as late_count\n        FROM attendance\n        WHERE date = ?\n      ","service":"sms-backend","timestamp":"2025-07-26 05:12:49"}
{"error":"Unknown column 'type' in 'field list'","level":"error","message":"Database query error:","params":[],"query":"\n        SELECT title, start_date, start_time, type, location\n        FROM events\n        WHERE start_date >= CURDATE() AND status = 'active'\n        ORDER BY start_date ASC, start_time ASC\n        LIMIT 5\n      ","service":"sms-backend","timestamp":"2025-07-26 05:12:49"}
{"level":"info","message":"::1 - - [26/Jul/2025:16:12:49 +0000] \"GET /api/analytics/dashboard HTTP/1.1\" 304 - \"http://localhost:3001/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-26 05:12:49"}
{"error":"Unknown column 'date' in 'where clause'","level":"error","message":"Database query error:","params":["2025-07-26"],"query":"\n        SELECT\n          COUNT(*) as total_records,\n          SUM(CASE WHEN status = 'present' THEN 1 ELSE 0 END) as present_count,\n          SUM(CASE WHEN status = 'absent' THEN 1 ELSE 0 END) as absent_count,\n          SUM(CASE WHEN status = 'late' THEN 1 ELSE 0 END) as late_count\n        FROM attendance\n        WHERE date = ?\n      ","service":"sms-backend","timestamp":"2025-07-26 05:12:49"}
{"error":"Unknown column 'type' in 'field list'","level":"error","message":"Database query error:","params":[],"query":"\n        SELECT title, start_date, start_time, type, location\n        FROM events\n        WHERE start_date >= CURDATE() AND status = 'active'\n        ORDER BY start_date ASC, start_time ASC\n        LIMIT 5\n      ","service":"sms-backend","timestamp":"2025-07-26 05:12:49"}
{"level":"info","message":"::1 - - [26/Jul/2025:16:12:49 +0000] \"GET /api/analytics/dashboard HTTP/1.1\" 304 - \"http://localhost:3001/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-26 05:12:49"}
{"error":"Unknown column 'c.grade_level_id' in 'on clause'","level":"error","message":"Database query error:","params":[],"query":"\n      SELECT COUNT(*) as total\n      FROM classes c\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      LEFT JOIN teachers t ON c.class_teacher_id = t.id\n      WHERE 1=1\n    ","service":"sms-backend","timestamp":"2025-07-26 05:16:47"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Get classes error: Unknown column 'c.grade_level_id' in 'on clause'","service":"sms-backend","sql":"\n      SELECT COUNT(*) as total\n      FROM classes c\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      LEFT JOIN teachers t ON c.class_teacher_id = t.id\n      WHERE 1=1\n    ","sqlMessage":"Unknown column 'c.grade_level_id' in 'on clause'","sqlState":"42S22","stack":"Error: Unknown column 'c.grade_level_id' in 'on clause'\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\config\\database.js:48:31)\n    at getClasses (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\classController.js:65:31)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at handleValidationErrors (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\utils\\validation.js:21:3)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\route.js:149:13)","timestamp":"2025-07-26 05:16:47"}
{"level":"info","message":"::1 - - [26/Jul/2025:16:16:47 +0000] \"GET /api/classes?page=1&limit=10&search=&sort_by=name&sort_order=ASC HTTP/1.1\" 500 56 \"http://localhost:3001/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-26 05:16:47"}
{"error":"Unknown column 'c.grade_level_id' in 'on clause'","level":"error","message":"Database query error:","params":[],"query":"\n      SELECT COUNT(*) as total\n      FROM classes c\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      LEFT JOIN teachers t ON c.class_teacher_id = t.id\n      WHERE 1=1\n    ","service":"sms-backend","timestamp":"2025-07-26 05:16:47"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Get classes error: Unknown column 'c.grade_level_id' in 'on clause'","service":"sms-backend","sql":"\n      SELECT COUNT(*) as total\n      FROM classes c\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      LEFT JOIN teachers t ON c.class_teacher_id = t.id\n      WHERE 1=1\n    ","sqlMessage":"Unknown column 'c.grade_level_id' in 'on clause'","sqlState":"42S22","stack":"Error: Unknown column 'c.grade_level_id' in 'on clause'\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\config\\database.js:48:31)\n    at getClasses (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\classController.js:65:31)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at handleValidationErrors (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\utils\\validation.js:21:3)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\route.js:149:13)","timestamp":"2025-07-26 05:16:47"}
{"level":"info","message":"::1 - - [26/Jul/2025:16:16:47 +0000] \"GET /api/classes?page=1&limit=10&search=&sort_by=name&sort_order=ASC HTTP/1.1\" 500 56 \"http://localhost:3001/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-26 05:16:47"}
{"error":"Table 'school_management_system.admins' doesn't exist","level":"error","message":"Database query error:","params":[2],"query":"SELECT * FROM admins WHERE user_id = ?","service":"sms-backend","timestamp":"2025-07-26 05:21:51"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Get profile error: Table 'school_management_system.admins' doesn't exist","service":"sms-backend","sql":"SELECT * FROM admins WHERE user_id = ?","sqlMessage":"Table 'school_management_system.admins' doesn't exist","sqlState":"42S02","stack":"Error: Table 'school_management_system.admins' doesn't exist\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\config\\database.js:48:31)\n    at getProfile (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\authController.js:256:30)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-26 05:21:51"}
{"level":"info","message":"::1 - - [26/Jul/2025:16:21:51 +0000] \"GET /api/auth/profile HTTP/1.1\" 500 51 \"http://localhost:3001/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-26 05:21:51"}
{"error":"Unknown column 'c.grade_level_id' in 'on clause'","level":"error","message":"Database query error:","params":[],"query":"\n      SELECT COUNT(*) as total\n      FROM classes c\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      LEFT JOIN teachers t ON c.class_teacher_id = t.id\n      WHERE 1=1\n    ","service":"sms-backend","timestamp":"2025-07-26 05:21:51"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Get classes error: Unknown column 'c.grade_level_id' in 'on clause'","service":"sms-backend","sql":"\n      SELECT COUNT(*) as total\n      FROM classes c\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      LEFT JOIN teachers t ON c.class_teacher_id = t.id\n      WHERE 1=1\n    ","sqlMessage":"Unknown column 'c.grade_level_id' in 'on clause'","sqlState":"42S22","stack":"Error: Unknown column 'c.grade_level_id' in 'on clause'\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\config\\database.js:48:31)\n    at getClasses (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\classController.js:65:31)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at handleValidationErrors (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\utils\\validation.js:21:3)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\route.js:149:13)","timestamp":"2025-07-26 05:21:51"}
{"level":"info","message":"::1 - - [26/Jul/2025:16:21:51 +0000] \"GET /api/classes?page=1&limit=10&search=&sort_by=name&sort_order=ASC HTTP/1.1\" 500 56 \"http://localhost:3001/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-26 05:21:51"}
{"error":"Unknown column 'c.grade_level_id' in 'on clause'","level":"error","message":"Database query error:","params":[],"query":"\n      SELECT COUNT(*) as total\n      FROM classes c\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      LEFT JOIN teachers t ON c.class_teacher_id = t.id\n      WHERE 1=1\n    ","service":"sms-backend","timestamp":"2025-07-26 05:21:51"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Get classes error: Unknown column 'c.grade_level_id' in 'on clause'","service":"sms-backend","sql":"\n      SELECT COUNT(*) as total\n      FROM classes c\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      LEFT JOIN teachers t ON c.class_teacher_id = t.id\n      WHERE 1=1\n    ","sqlMessage":"Unknown column 'c.grade_level_id' in 'on clause'","sqlState":"42S22","stack":"Error: Unknown column 'c.grade_level_id' in 'on clause'\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\config\\database.js:48:31)\n    at getClasses (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\classController.js:65:31)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at handleValidationErrors (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\utils\\validation.js:21:3)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\route.js:149:13)","timestamp":"2025-07-26 05:21:51"}
{"level":"info","message":"::1 - - [26/Jul/2025:16:21:51 +0000] \"GET /api/classes?page=1&limit=10&search=&sort_by=name&sort_order=ASC HTTP/1.1\" 500 56 \"http://localhost:3001/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-26 05:21:51"}
{"level":"info","message":"::1 - - [26/Jul/2025:16:22:04 +0000] \"GET /api/students?page=1&limit=10&search=&sort_by=first_name&sort_order=ASC HTTP/1.1\" 304 - \"http://localhost:3001/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-26 05:22:04"}
{"level":"info","message":"::1 - - [26/Jul/2025:16:22:04 +0000] \"GET /api/students?page=1&limit=10&search=&sort_by=first_name&sort_order=ASC HTTP/1.1\" 304 - \"http://localhost:3001/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-26 05:22:04"}
{"error":"Unknown column 'c.grade_level_id' in 'on clause'","level":"error","message":"Database query error:","params":[],"query":"\n      SELECT COUNT(*) as total\n      FROM classes c\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      LEFT JOIN teachers t ON c.class_teacher_id = t.id\n      WHERE 1=1\n    ","service":"sms-backend","timestamp":"2025-07-26 05:22:06"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Get classes error: Unknown column 'c.grade_level_id' in 'on clause'","service":"sms-backend","sql":"\n      SELECT COUNT(*) as total\n      FROM classes c\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      LEFT JOIN teachers t ON c.class_teacher_id = t.id\n      WHERE 1=1\n    ","sqlMessage":"Unknown column 'c.grade_level_id' in 'on clause'","sqlState":"42S22","stack":"Error: Unknown column 'c.grade_level_id' in 'on clause'\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\config\\database.js:48:31)\n    at getClasses (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\classController.js:65:31)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at handleValidationErrors (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\utils\\validation.js:21:3)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\route.js:149:13)","timestamp":"2025-07-26 05:22:06"}
{"level":"info","message":"::1 - - [26/Jul/2025:16:22:06 +0000] \"GET /api/classes HTTP/1.1\" 500 56 \"http://localhost:3001/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-26 05:22:06"}
{"error":"Unknown column 'c.grade_level_id' in 'on clause'","level":"error","message":"Database query error:","params":[],"query":"\n      SELECT COUNT(*) as total\n      FROM classes c\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      LEFT JOIN teachers t ON c.class_teacher_id = t.id\n      WHERE 1=1\n    ","service":"sms-backend","timestamp":"2025-07-26 05:25:39"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Get classes error: Unknown column 'c.grade_level_id' in 'on clause'","service":"sms-backend","sql":"\n      SELECT COUNT(*) as total\n      FROM classes c\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      LEFT JOIN teachers t ON c.class_teacher_id = t.id\n      WHERE 1=1\n    ","sqlMessage":"Unknown column 'c.grade_level_id' in 'on clause'","sqlState":"42S22","stack":"Error: Unknown column 'c.grade_level_id' in 'on clause'\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\config\\database.js:48:31)\n    at getClasses (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\classController.js:65:31)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at handleValidationErrors (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\utils\\validation.js:21:3)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\route.js:149:13)","timestamp":"2025-07-26 05:25:39"}
{"level":"info","message":"::1 - - [26/Jul/2025:16:25:39 +0000] \"GET /api/classes?page=1&limit=10&search=&sort_by=name&sort_order=ASC HTTP/1.1\" 500 56 \"http://localhost:3001/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-26 05:25:39"}
{"error":"Unknown column 'c.grade_level_id' in 'on clause'","level":"error","message":"Database query error:","params":[],"query":"\n      SELECT COUNT(*) as total\n      FROM classes c\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      LEFT JOIN teachers t ON c.class_teacher_id = t.id\n      WHERE 1=1\n    ","service":"sms-backend","timestamp":"2025-07-26 05:25:39"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Get classes error: Unknown column 'c.grade_level_id' in 'on clause'","service":"sms-backend","sql":"\n      SELECT COUNT(*) as total\n      FROM classes c\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      LEFT JOIN teachers t ON c.class_teacher_id = t.id\n      WHERE 1=1\n    ","sqlMessage":"Unknown column 'c.grade_level_id' in 'on clause'","sqlState":"42S22","stack":"Error: Unknown column 'c.grade_level_id' in 'on clause'\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\config\\database.js:48:31)\n    at getClasses (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\classController.js:65:31)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at handleValidationErrors (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\utils\\validation.js:21:3)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\route.js:149:13)","timestamp":"2025-07-26 05:25:39"}
{"level":"info","message":"::1 - - [26/Jul/2025:16:25:39 +0000] \"GET /api/classes?page=1&limit=10&search=&sort_by=name&sort_order=ASC HTTP/1.1\" 500 56 \"http://localhost:3001/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-26 05:25:39"}
{"level":"info","message":"::1 - - [26/Jul/2025:16:37:17 +0000] \"POST /api/classes HTTP/1.1\" 400 208 \"http://localhost:3001/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-26 05:37:17"}
{"level":"info","message":"::1 - - [26/Jul/2025:16:37:17 +0000] \"POST /api/classes HTTP/1.1\" 400 208 \"http://localhost:3001/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-26 05:37:17"}
{"error":"Table 'school_management_system.admins' doesn't exist","level":"error","message":"Database query error:","params":[2],"query":"SELECT * FROM admins WHERE user_id = ?","service":"sms-backend","timestamp":"2025-07-26 05:37:23"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Get profile error: Table 'school_management_system.admins' doesn't exist","service":"sms-backend","sql":"SELECT * FROM admins WHERE user_id = ?","sqlMessage":"Table 'school_management_system.admins' doesn't exist","sqlState":"42S02","stack":"Error: Table 'school_management_system.admins' doesn't exist\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\config\\database.js:48:31)\n    at getProfile (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\authController.js:256:30)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-26 05:37:23"}
{"level":"info","message":"::1 - - [26/Jul/2025:16:37:23 +0000] \"GET /api/auth/profile HTTP/1.1\" 500 51 \"http://localhost:3001/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-26 05:37:23"}
{"error":"Unknown column 'c.grade_level_id' in 'on clause'","level":"error","message":"Database query error:","params":[],"query":"\n      SELECT COUNT(*) as total\n      FROM classes c\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      LEFT JOIN teachers t ON c.class_teacher_id = t.id\n      WHERE 1=1\n    ","service":"sms-backend","timestamp":"2025-07-26 05:37:23"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Get classes error: Unknown column 'c.grade_level_id' in 'on clause'","service":"sms-backend","sql":"\n      SELECT COUNT(*) as total\n      FROM classes c\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      LEFT JOIN teachers t ON c.class_teacher_id = t.id\n      WHERE 1=1\n    ","sqlMessage":"Unknown column 'c.grade_level_id' in 'on clause'","sqlState":"42S22","stack":"Error: Unknown column 'c.grade_level_id' in 'on clause'\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\config\\database.js:48:31)\n    at getClasses (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\classController.js:65:31)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at handleValidationErrors (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\utils\\validation.js:21:3)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\route.js:149:13)","timestamp":"2025-07-26 05:37:23"}
{"level":"info","message":"::1 - - [26/Jul/2025:16:37:23 +0000] \"GET /api/classes?page=1&limit=10&search=&sort_by=name&sort_order=ASC HTTP/1.1\" 500 56 \"http://localhost:3001/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-26 05:37:23"}
{"error":"Unknown column 'c.grade_level_id' in 'on clause'","level":"error","message":"Database query error:","params":[],"query":"\n      SELECT COUNT(*) as total\n      FROM classes c\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      LEFT JOIN teachers t ON c.class_teacher_id = t.id\n      WHERE 1=1\n    ","service":"sms-backend","timestamp":"2025-07-26 05:37:23"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Get classes error: Unknown column 'c.grade_level_id' in 'on clause'","service":"sms-backend","sql":"\n      SELECT COUNT(*) as total\n      FROM classes c\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      LEFT JOIN teachers t ON c.class_teacher_id = t.id\n      WHERE 1=1\n    ","sqlMessage":"Unknown column 'c.grade_level_id' in 'on clause'","sqlState":"42S22","stack":"Error: Unknown column 'c.grade_level_id' in 'on clause'\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\config\\database.js:48:31)\n    at getClasses (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\classController.js:65:31)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at handleValidationErrors (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\utils\\validation.js:21:3)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\route.js:149:13)","timestamp":"2025-07-26 05:37:23"}
{"level":"info","message":"::1 - - [26/Jul/2025:16:37:23 +0000] \"GET /api/classes?page=1&limit=10&search=&sort_by=name&sort_order=ASC HTTP/1.1\" 500 56 \"http://localhost:3001/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-26 05:37:23"}
{"error":"Table 'school_management_system.admins' doesn't exist","level":"error","message":"Database query error:","params":[2],"query":"SELECT * FROM admins WHERE user_id = ?","service":"sms-backend","timestamp":"2025-07-26 05:38:20"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Get profile error: Table 'school_management_system.admins' doesn't exist","service":"sms-backend","sql":"SELECT * FROM admins WHERE user_id = ?","sqlMessage":"Table 'school_management_system.admins' doesn't exist","sqlState":"42S02","stack":"Error: Table 'school_management_system.admins' doesn't exist\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\config\\database.js:48:31)\n    at getProfile (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\authController.js:256:30)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-26 05:38:20"}
{"level":"info","message":"::1 - - [26/Jul/2025:16:38:20 +0000] \"GET /api/auth/profile HTTP/1.1\" 500 51 \"http://localhost:3001/\" \"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-26 05:38:20"}
{"error":"Unknown column 'c.grade_level_id' in 'on clause'","level":"error","message":"Database query error:","params":[],"query":"\n      SELECT COUNT(*) as total\n      FROM classes c\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      LEFT JOIN teachers t ON c.class_teacher_id = t.id\n      WHERE 1=1\n    ","service":"sms-backend","timestamp":"2025-07-26 05:38:20"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Get classes error: Unknown column 'c.grade_level_id' in 'on clause'","service":"sms-backend","sql":"\n      SELECT COUNT(*) as total\n      FROM classes c\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      LEFT JOIN teachers t ON c.class_teacher_id = t.id\n      WHERE 1=1\n    ","sqlMessage":"Unknown column 'c.grade_level_id' in 'on clause'","sqlState":"42S22","stack":"Error: Unknown column 'c.grade_level_id' in 'on clause'\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\config\\database.js:48:31)\n    at getClasses (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\classController.js:65:31)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at handleValidationErrors (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\utils\\validation.js:21:3)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\route.js:149:13)","timestamp":"2025-07-26 05:38:20"}
{"level":"info","message":"::1 - - [26/Jul/2025:16:38:20 +0000] \"GET /api/classes?page=1&limit=10&search=&sort_by=name&sort_order=ASC HTTP/1.1\" 500 56 \"http://localhost:3001/\" \"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-26 05:38:20"}
{"error":"Unknown column 'c.grade_level_id' in 'on clause'","level":"error","message":"Database query error:","params":[],"query":"\n      SELECT COUNT(*) as total\n      FROM classes c\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      LEFT JOIN teachers t ON c.class_teacher_id = t.id\n      WHERE 1=1\n    ","service":"sms-backend","timestamp":"2025-07-26 05:38:20"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Get classes error: Unknown column 'c.grade_level_id' in 'on clause'","service":"sms-backend","sql":"\n      SELECT COUNT(*) as total\n      FROM classes c\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      LEFT JOIN teachers t ON c.class_teacher_id = t.id\n      WHERE 1=1\n    ","sqlMessage":"Unknown column 'c.grade_level_id' in 'on clause'","sqlState":"42S22","stack":"Error: Unknown column 'c.grade_level_id' in 'on clause'\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\config\\database.js:48:31)\n    at getClasses (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\classController.js:65:31)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at handleValidationErrors (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\utils\\validation.js:21:3)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\route.js:149:13)","timestamp":"2025-07-26 05:38:20"}
{"level":"info","message":"::1 - - [26/Jul/2025:16:38:20 +0000] \"GET /api/classes?page=1&limit=10&search=&sort_by=name&sort_order=ASC HTTP/1.1\" 500 56 \"http://localhost:3001/\" \"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-26 05:38:20"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-26 05:40:11"}
{"level":"info","message":"Database connected successfully","service":"sms-backend","timestamp":"2025-07-26 05:40:11"}
{"level":"info","message":"Server running on port 5000 in development mode","service":"sms-backend","timestamp":"2025-07-26 05:40:11"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-26 05:52:31"}
{"level":"info","message":"Database connected successfully","service":"sms-backend","timestamp":"2025-07-26 05:52:31"}
{"level":"info","message":"Server running on port 5000 in development mode","service":"sms-backend","timestamp":"2025-07-26 05:52:31"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-26 05:53:57"}
{"level":"info","message":"Database connected successfully","service":"sms-backend","timestamp":"2025-07-26 05:53:57"}
{"level":"info","message":"Server running on port 5000 in development mode","service":"sms-backend","timestamp":"2025-07-26 05:53:57"}
{"level":"info","message":"User <EMAIL> logged in successfully","service":"sms-backend","timestamp":"2025-07-26 05:54:55"}
{"level":"info","message":"::1 - - [26/Jul/2025:16:54:55 +0000] \"POST /api/auth/login HTTP/1.1\" 200 846 \"-\" \"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.19041.6093\"","service":"sms-backend","timestamp":"2025-07-26 05:54:55"}
{"error":"Unknown column 'date' in 'where clause'","level":"error","message":"Database query error:","params":["2025-07-26"],"query":"\n        SELECT\n          COUNT(*) as total_records,\n          SUM(CASE WHEN status = 'present' THEN 1 ELSE 0 END) as present_count,\n          SUM(CASE WHEN status = 'absent' THEN 1 ELSE 0 END) as absent_count,\n          SUM(CASE WHEN status = 'late' THEN 1 ELSE 0 END) as late_count\n        FROM attendance\n        WHERE date = ?\n      ","service":"sms-backend","timestamp":"2025-07-26 05:54:55"}
{"error":"Unknown column 'type' in 'field list'","level":"error","message":"Database query error:","params":[],"query":"\n        SELECT title, start_date, start_time, type, location\n        FROM events\n        WHERE start_date >= CURDATE() AND status = 'active'\n        ORDER BY start_date ASC, start_time ASC\n        LIMIT 5\n      ","service":"sms-backend","timestamp":"2025-07-26 05:54:55"}
{"level":"info","message":"::1 - - [26/Jul/2025:16:54:55 +0000] \"GET /api/analytics/dashboard HTTP/1.1\" 200 367 \"-\" \"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.19041.6093\"","service":"sms-backend","timestamp":"2025-07-26 05:54:55"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-26 05:55:37"}
{"level":"info","message":"Database connected successfully","service":"sms-backend","timestamp":"2025-07-26 05:55:37"}
{"level":"info","message":"Server running on port 5000 in development mode","service":"sms-backend","timestamp":"2025-07-26 05:55:37"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-26 05:55:37"}
{"level":"info","message":"Database connection pool created successfully","service":"sms-backend","timestamp":"2025-07-26 05:56:17"}
{"level":"info","message":"Database connected successfully","service":"sms-backend","timestamp":"2025-07-26 05:56:17"}
{"level":"info","message":"Server running on port 5000 in development mode","service":"sms-backend","timestamp":"2025-07-26 05:56:18"}
{"error":"Table 'school_management_system.admins' doesn't exist","level":"error","message":"Database query error:","params":[2],"query":"SELECT * FROM admins WHERE user_id = ?","service":"sms-backend","timestamp":"2025-07-26 05:57:37"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Get profile error: Table 'school_management_system.admins' doesn't exist","service":"sms-backend","sql":"SELECT * FROM admins WHERE user_id = ?","sqlMessage":"Table 'school_management_system.admins' doesn't exist","sqlState":"42S02","stack":"Error: Table 'school_management_system.admins' doesn't exist\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\config\\database.js:48:31)\n    at getProfile (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\authController.js:256:30)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-26 05:57:37"}
{"level":"info","message":"::1 - - [26/Jul/2025:16:57:37 +0000] \"GET /api/auth/profile HTTP/1.1\" 500 51 \"http://localhost:3001/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-26 05:57:37"}
{"error":"Unknown column 'date' in 'where clause'","level":"error","message":"Database query error:","params":["2025-07-26"],"query":"\n        SELECT\n          COUNT(*) as total_records,\n          SUM(CASE WHEN status = 'present' THEN 1 ELSE 0 END) as present_count,\n          SUM(CASE WHEN status = 'absent' THEN 1 ELSE 0 END) as absent_count,\n          SUM(CASE WHEN status = 'late' THEN 1 ELSE 0 END) as late_count\n        FROM attendance\n        WHERE date = ?\n      ","service":"sms-backend","timestamp":"2025-07-26 05:57:37"}
{"error":"Unknown column 'type' in 'field list'","level":"error","message":"Database query error:","params":[],"query":"\n        SELECT title, start_date, start_time, type, location\n        FROM events\n        WHERE start_date >= CURDATE() AND status = 'active'\n        ORDER BY start_date ASC, start_time ASC\n        LIMIT 5\n      ","service":"sms-backend","timestamp":"2025-07-26 05:57:37"}
{"level":"info","message":"::1 - - [26/Jul/2025:16:57:37 +0000] \"GET /api/analytics/dashboard HTTP/1.1\" 304 - \"http://localhost:3001/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-26 05:57:37"}
{"error":"Unknown column 'date' in 'where clause'","level":"error","message":"Database query error:","params":["2025-07-26"],"query":"\n        SELECT\n          COUNT(*) as total_records,\n          SUM(CASE WHEN status = 'present' THEN 1 ELSE 0 END) as present_count,\n          SUM(CASE WHEN status = 'absent' THEN 1 ELSE 0 END) as absent_count,\n          SUM(CASE WHEN status = 'late' THEN 1 ELSE 0 END) as late_count\n        FROM attendance\n        WHERE date = ?\n      ","service":"sms-backend","timestamp":"2025-07-26 05:57:37"}
{"error":"Unknown column 'type' in 'field list'","level":"error","message":"Database query error:","params":[],"query":"\n        SELECT title, start_date, start_time, type, location\n        FROM events\n        WHERE start_date >= CURDATE() AND status = 'active'\n        ORDER BY start_date ASC, start_time ASC\n        LIMIT 5\n      ","service":"sms-backend","timestamp":"2025-07-26 05:57:37"}
{"level":"info","message":"::1 - - [26/Jul/2025:16:57:37 +0000] \"GET /api/analytics/dashboard HTTP/1.1\" 304 - \"http://localhost:3001/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-26 05:57:37"}
{"error":"Table 'school_management_system.admins' doesn't exist","level":"error","message":"Database query error:","params":[2],"query":"SELECT * FROM admins WHERE user_id = ?","service":"sms-backend","timestamp":"2025-07-26 05:57:42"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Get profile error: Table 'school_management_system.admins' doesn't exist","service":"sms-backend","sql":"SELECT * FROM admins WHERE user_id = ?","sqlMessage":"Table 'school_management_system.admins' doesn't exist","sqlState":"42S02","stack":"Error: Table 'school_management_system.admins' doesn't exist\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\config\\database.js:48:31)\n    at getProfile (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\authController.js:256:30)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-26 05:57:42"}
{"level":"info","message":"::1 - - [26/Jul/2025:16:57:42 +0000] \"GET /api/auth/profile HTTP/1.1\" 500 51 \"http://localhost:3001/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-26 05:57:42"}
{"error":"Unknown column 'date' in 'where clause'","level":"error","message":"Database query error:","params":["2025-07-26"],"query":"\n        SELECT\n          COUNT(*) as total_records,\n          SUM(CASE WHEN status = 'present' THEN 1 ELSE 0 END) as present_count,\n          SUM(CASE WHEN status = 'absent' THEN 1 ELSE 0 END) as absent_count,\n          SUM(CASE WHEN status = 'late' THEN 1 ELSE 0 END) as late_count\n        FROM attendance\n        WHERE date = ?\n      ","service":"sms-backend","timestamp":"2025-07-26 05:57:42"}
{"error":"Unknown column 'type' in 'field list'","level":"error","message":"Database query error:","params":[],"query":"\n        SELECT title, start_date, start_time, type, location\n        FROM events\n        WHERE start_date >= CURDATE() AND status = 'active'\n        ORDER BY start_date ASC, start_time ASC\n        LIMIT 5\n      ","service":"sms-backend","timestamp":"2025-07-26 05:57:42"}
{"level":"info","message":"::1 - - [26/Jul/2025:16:57:42 +0000] \"GET /api/analytics/dashboard HTTP/1.1\" 304 - \"http://localhost:3001/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-26 05:57:42"}
{"error":"Unknown column 'date' in 'where clause'","level":"error","message":"Database query error:","params":["2025-07-26"],"query":"\n        SELECT\n          COUNT(*) as total_records,\n          SUM(CASE WHEN status = 'present' THEN 1 ELSE 0 END) as present_count,\n          SUM(CASE WHEN status = 'absent' THEN 1 ELSE 0 END) as absent_count,\n          SUM(CASE WHEN status = 'late' THEN 1 ELSE 0 END) as late_count\n        FROM attendance\n        WHERE date = ?\n      ","service":"sms-backend","timestamp":"2025-07-26 05:57:42"}
{"error":"Unknown column 'type' in 'field list'","level":"error","message":"Database query error:","params":[],"query":"\n        SELECT title, start_date, start_time, type, location\n        FROM events\n        WHERE start_date >= CURDATE() AND status = 'active'\n        ORDER BY start_date ASC, start_time ASC\n        LIMIT 5\n      ","service":"sms-backend","timestamp":"2025-07-26 05:57:42"}
{"level":"info","message":"::1 - - [26/Jul/2025:16:57:42 +0000] \"GET /api/analytics/dashboard HTTP/1.1\" 304 - \"http://localhost:3001/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"sms-backend","timestamp":"2025-07-26 05:57:42"}
